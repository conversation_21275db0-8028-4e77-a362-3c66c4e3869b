LOAD:0000000000BEC368                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC36C                 AND             X28, X28, #0xFF
LOAD:0000000000BEC370                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC374                 LDR             X1, [X20,X28,LSL#3]
LOAD:0000000000BEC378                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC37C                 B.NE            loc_BEC3B0
LOAD:0000000000BEC380                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC384                 B.NE            loc_BEC3B0
LOAD:0000000000BEC388                 ADDS            W0, W0, W1
LOAD:0000000000BEC38C                 B.VS            loc_BEE368
LOAD:0000000000BEC390                 ADD             X0, X0, X24
LOAD:0000000000BEC394                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC398
LOAD:0000000000BEC398 loc_BEC398                              ; CODE XREF: LOAD:0000000000BEC3D0↓j
LOAD:0000000000BEC398                 LDR             W16, [X21],#4
LOAD:0000000000BEC39C                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC3A0                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC3A4                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC3A8                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC3AC                 BR              X8
LOAD:0000000000BEC3B0 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC3B0
LOAD:0000000000BEC3B0 loc_BEC3B0                              ; CODE XREF: LOAD:0000000000BEC37C↑j
LOAD:0000000000BEC3B0                                         ; LOAD:0000000000BEC384↑j
LOAD:0000000000BEC3B0                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC3B4                 LDR             D1, [X20,X28,LSL#3]
LOAD:0000000000BEC3B8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC3BC                 B.LS            loc_BEE368
LOAD:0000000000BEC3C0                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC3C4                 B.LS            loc_BEE368
LOAD:0000000000BEC3C8                 FADD            D0, D0, D1
LOAD:0000000000BEC3CC                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC3D0                 B               loc_BEC398
LOAD:0000000000BEC3D4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC3D4
LOAD:0000000000BEC3D4 BC_SUBVN
LOAD:0000000000BEC3D4                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC3D8                 AND             X28, X28, #0xFF
LOAD:0000000000BEC3DC                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC3E0                 LDR             X1, [X20,X28,LSL#3]
LOAD:0000000000BEC3E4                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC3E8                 B.NE            loc_BEC41C
LOAD:0000000000BEC3EC                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC3F0                 B.NE            loc_BEC41C
LOAD:0000000000BEC3F4                 SUBS            W0, W0, W1
LOAD:0000000000BEC3F8                 B.VS            loc_BEE368
LOAD:0000000000BEC3FC                 ADD             X0, X0, X24
LOAD:0000000000BEC400                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC404
LOAD:0000000000BEC404 loc_BEC404                              ; CODE XREF: LOAD:0000000000BEC43C↓j
LOAD:0000000000BEC404                 LDR             W16, [X21],#4
LOAD:0000000000BEC408                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC40C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC410                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC414                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC418                 BR              X8
LOAD:0000000000BEC41C ; ---------------------------------------------------------------------------
LOAD:0000000000BEC41C
LOAD:0000000000BEC41C loc_BEC41C                              ; CODE XREF: LOAD:0000000000BEC3E8↑j
LOAD:0000000000BEC41C                                         ; LOAD:0000000000BEC3F0↑j
LOAD:0000000000BEC41C                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC420                 LDR             D1, [X20,X28,LSL#3]
LOAD:0000000000BEC424                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC428                 B.LS            loc_BEE368
LOAD:0000000000BEC42C                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC430                 B.LS            loc_BEE368
LOAD:0000000000BEC434                 FSUB            D0, D0, D1
LOAD:0000000000BEC438                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC43C                 B               loc_BEC404
LOAD:0000000000BEC440 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC440
LOAD:0000000000BEC440 BC_MULVN
LOAD:0000000000BEC440                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC444                 AND             X28, X28, #0xFF
LOAD:0000000000BEC448                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC44C                 LDR             X1, [X20,X28,LSL#3]
LOAD:0000000000BEC450                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC454                 B.NE            loc_BEC490
LOAD:0000000000BEC458                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC45C                 B.NE            loc_BEC490
LOAD:0000000000BEC460                 SMULL           X0, W0, W1
LOAD:0000000000BEC464                 CMP             X0, W0,SXTW
LOAD:0000000000BEC468                 MOV             W0, W0
LOAD:0000000000BEC46C                 B.NE            loc_BEE368
LOAD:0000000000BEC470                 ADD             X0, X0, X24
LOAD:0000000000BEC474                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC478
LOAD:0000000000BEC478 loc_BEC478                              ; CODE XREF: LOAD:0000000000BEC4B0↓j
LOAD:0000000000BEC478                 LDR             W16, [X21],#4
LOAD:0000000000BEC47C                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC480                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC484                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC488                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC48C                 BR              X8
LOAD:0000000000BEC490 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC490
LOAD:0000000000BEC490 loc_BEC490                              ; CODE XREF: LOAD:0000000000BEC454↑j
LOAD:0000000000BEC490                                         ; LOAD:0000000000BEC45C↑j
LOAD:0000000000BEC490                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC494                 LDR             D1, [X20,X28,LSL#3]
LOAD:0000000000BEC498                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC49C                 B.LS            loc_BEE368
LOAD:0000000000BEC4A0                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC4A4                 B.LS            loc_BEE368
LOAD:0000000000BEC4A8                 FMUL            D0, D0, D1
LOAD:0000000000BEC4AC                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC4B0                 B               loc_BEC478
LOAD:0000000000BEC4B4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC4B4
LOAD:0000000000BEC4B4 BC_DIVVN
LOAD:0000000000BEC4B4                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC4B8                 AND             X28, X28, #0xFF
LOAD:0000000000BEC4BC                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC4C0                 LDR             X1, [X20,X28,LSL#3]
LOAD:0000000000BEC4C4                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC4C8                 LDR             D1, [X20,X28,LSL#3]
LOAD:0000000000BEC4CC                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC4D0                 B.LS            loc_BEE368
LOAD:0000000000BEC4D4                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC4D8                 B.LS            loc_BEE368
LOAD:0000000000BEC4DC                 FDIV            D0, D0, D1
LOAD:0000000000BEC4E0                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC4E4                 LDR             W16, [X21],#4
LOAD:0000000000BEC4E8                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC4EC                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC4F0                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC4F4                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC4F8                 BR              X8
LOAD:0000000000BEC4FC ; ---------------------------------------------------------------------------
LOAD:0000000000BEC4FC
LOAD:0000000000BEC4FC BC_MODVN
LOAD:0000000000BEC4FC                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC500                 AND             X28, X28, #0xFF
LOAD:0000000000BEC504                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC508                 LDR             X1, [X20,X28,LSL#3]
LOAD:0000000000BEC50C                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC510                 B.NE            loc_BEC544
LOAD:0000000000BEC514                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC518                 B.NE            loc_BEC544
LOAD:0000000000BEC51C                 CBZ             W1, loc_BEE368
LOAD:0000000000BEC520                 BL              sub_BEF7FC
LOAD:0000000000BEC524                 ADD             X0, X0, X24
LOAD:0000000000BEC528                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC52C
LOAD:0000000000BEC52C loc_BEC52C                              ; CODE XREF: LOAD:0000000000BEC56C↓j
LOAD:0000000000BEC52C                 LDR             W16, [X21],#4
LOAD:0000000000BEC530                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC534                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC538                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC53C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC540                 BR              X8
LOAD:0000000000BEC544 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC544
LOAD:0000000000BEC544 loc_BEC544                              ; CODE XREF: LOAD:0000000000BEC510↑j
LOAD:0000000000BEC544                                         ; LOAD:0000000000BEC518↑j
LOAD:0000000000BEC544                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC548                 LDR             D1, [X20,X28,LSL#3]
LOAD:0000000000BEC54C                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC550                 B.LS            loc_BEE368
LOAD:0000000000BEC554                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC558                 B.LS            loc_BEE368
LOAD:0000000000BEC55C                 FDIV            D2, D0, D1
LOAD:0000000000BEC560                 FRINTM          D2, D2
LOAD:0000000000BEC564                 FMSUB           D0, D2, D1, D0
LOAD:0000000000BEC568                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC56C                 B               loc_BEC52C
LOAD:0000000000BEC570 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC570
LOAD:0000000000BEC570 BC_ADDNV
LOAD:0000000000BEC570                 AND             X28, X28, #0xFF
LOAD:0000000000BEC574                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC578                 LDR             X0, [X20,X28,LSL#3]
LOAD:0000000000BEC57C                 LDR             X1, [X19,X17,LSL#3]
LOAD:0000000000BEC580                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC584                 B.NE            loc_BEC5B8
LOAD:0000000000BEC588                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC58C                 B.NE            loc_BEC5B8
LOAD:0000000000BEC590                 ADDS            W0, W0, W1
LOAD:0000000000BEC594                 B.VS            loc_BEE374
LOAD:0000000000BEC598                 ADD             X0, X0, X24
LOAD:0000000000BEC59C                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC5A0
LOAD:0000000000BEC5A0 loc_BEC5A0                              ; CODE XREF: LOAD:0000000000BEC5D8↓j
LOAD:0000000000BEC5A0                 LDR             W16, [X21],#4
LOAD:0000000000BEC5A4                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC5A8                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC5AC                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC5B0                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC5B4                 BR              X8
LOAD:0000000000BEC5B8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC5B8
LOAD:0000000000BEC5B8 loc_BEC5B8                              ; CODE XREF: LOAD:0000000000BEC584↑j
LOAD:0000000000BEC5B8                                         ; LOAD:0000000000BEC58C↑j
LOAD:0000000000BEC5B8                 LDR             D0, [X20,X28,LSL#3]
LOAD:0000000000BEC5BC                 LDR             D1, [X19,X17,LSL#3]
LOAD:0000000000BEC5C0                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC5C4                 B.LS            loc_BEE374
LOAD:0000000000BEC5C8                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC5CC                 B.LS            loc_BEE374
LOAD:0000000000BEC5D0                 FADD            D0, D0, D1
LOAD:0000000000BEC5D4                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC5D8                 B               loc_BEC5A0
LOAD:0000000000BEC5DC ; ---------------------------------------------------------------------------
LOAD:0000000000BEC5DC
LOAD:0000000000BEC5DC BC_SUBNV
LOAD:0000000000BEC5DC                 AND             X28, X28, #0xFF
LOAD:0000000000BEC5E0                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC5E4                 LDR             X0, [X20,X28,LSL#3]
LOAD:0000000000BEC5E8                 LDR             X1, [X19,X17,LSL#3]
LOAD:0000000000BEC5EC                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC5F0                 B.NE            loc_BEC624
LOAD:0000000000BEC5F4                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC5F8                 B.NE            loc_BEC624
LOAD:0000000000BEC5FC                 SUBS            W0, W0, W1
LOAD:0000000000BEC600                 B.VS            loc_BEE374
LOAD:0000000000BEC604                 ADD             X0, X0, X24
LOAD:0000000000BEC608                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC60C
LOAD:0000000000BEC60C loc_BEC60C                              ; CODE XREF: LOAD:0000000000BEC644↓j
LOAD:0000000000BEC60C                 LDR             W16, [X21],#4
LOAD:0000000000BEC610                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC614                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC618                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC61C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC620                 BR              X8
LOAD:0000000000BEC624 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC624
LOAD:0000000000BEC624 loc_BEC624                              ; CODE XREF: LOAD:0000000000BEC5F0↑j
LOAD:0000000000BEC624                                         ; LOAD:0000000000BEC5F8↑j
LOAD:0000000000BEC624                 LDR             D0, [X20,X28,LSL#3]
LOAD:0000000000BEC628                 LDR             D1, [X19,X17,LSL#3]
LOAD:0000000000BEC62C                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC630                 B.LS            loc_BEE374
LOAD:0000000000BEC634                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC638                 B.LS            loc_BEE374
LOAD:0000000000BEC63C                 FSUB            D0, D0, D1
LOAD:0000000000BEC640                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC644                 B               loc_BEC60C
LOAD:0000000000BEC648 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC648
LOAD:0000000000BEC648 BC_MULNV
LOAD:0000000000BEC648                 AND             X28, X28, #0xFF
LOAD:0000000000BEC64C                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC650                 LDR             X0, [X20,X28,LSL#3]
LOAD:0000000000BEC654                 LDR             X1, [X19,X17,LSL#3]
LOAD:0000000000BEC658                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC65C                 B.NE            loc_BEC698
LOAD:0000000000BEC660                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC664                 B.NE            loc_BEC698
LOAD:0000000000BEC668                 SMULL           X0, W0, W1
LOAD:0000000000BEC66C                 CMP             X0, W0,SXTW
LOAD:0000000000BEC670                 MOV             W0, W0
LOAD:0000000000BEC674                 B.NE            loc_BEE374
LOAD:0000000000BEC678                 ADD             X0, X0, X24
LOAD:0000000000BEC67C                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC680
LOAD:0000000000BEC680 loc_BEC680                              ; CODE XREF: LOAD:0000000000BEC6B8↓j
LOAD:0000000000BEC680                 LDR             W16, [X21],#4
LOAD:0000000000BEC684                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC688                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC68C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC690                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC694                 BR              X8
LOAD:0000000000BEC698 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC698
LOAD:0000000000BEC698 loc_BEC698                              ; CODE XREF: LOAD:0000000000BEC65C↑j
LOAD:0000000000BEC698                                         ; LOAD:0000000000BEC664↑j
LOAD:0000000000BEC698                 LDR             D0, [X20,X28,LSL#3]
LOAD:0000000000BEC69C                 LDR             D1, [X19,X17,LSL#3]
LOAD:0000000000BEC6A0                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC6A4                 B.LS            loc_BEE374
LOAD:0000000000BEC6A8                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC6AC                 B.LS            loc_BEE374
LOAD:0000000000BEC6B0                 FMUL            D0, D0, D1
LOAD:0000000000BEC6B4                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC6B8                 B               loc_BEC680
LOAD:0000000000BEC6BC ; ---------------------------------------------------------------------------
LOAD:0000000000BEC6BC
LOAD:0000000000BEC6BC BC_DIVNV
LOAD:0000000000BEC6BC                 AND             X28, X28, #0xFF
LOAD:0000000000BEC6C0                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC6C4                 LDR             X0, [X20,X28,LSL#3]
LOAD:0000000000BEC6C8                 LDR             X1, [X19,X17,LSL#3]
LOAD:0000000000BEC6CC                 LDR             D0, [X20,X28,LSL#3]
LOAD:0000000000BEC6D0                 LDR             D1, [X19,X17,LSL#3]
LOAD:0000000000BEC6D4                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC6D8                 B.LS            loc_BEE374
LOAD:0000000000BEC6DC                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC6E0                 B.LS            loc_BEE374
LOAD:0000000000BEC6E4                 FDIV            D0, D0, D1
LOAD:0000000000BEC6E8                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC6EC                 LDR             W16, [X21],#4
LOAD:0000000000BEC6F0                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC6F4                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC6F8                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC6FC                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC700                 BR              X8
LOAD:0000000000BEC704 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC704
LOAD:0000000000BEC704 BC_MODNV
LOAD:0000000000BEC704                 AND             X28, X28, #0xFF
LOAD:0000000000BEC708                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC70C                 LDR             X0, [X20,X28,LSL#3]
LOAD:0000000000BEC710                 LDR             X1, [X19,X17,LSL#3]
LOAD:0000000000BEC714                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC718                 B.NE            loc_BEC74C
LOAD:0000000000BEC71C                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC720                 B.NE            loc_BEC74C
LOAD:0000000000BEC724                 CBZ             W1, loc_BEE374
LOAD:0000000000BEC728                 BL              sub_BEF7FC
LOAD:0000000000BEC72C                 ADD             X0, X0, X24
LOAD:0000000000BEC730                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC734
LOAD:0000000000BEC734 loc_BEC734                              ; CODE XREF: LOAD:0000000000BEC774↓j
LOAD:0000000000BEC734                 LDR             W16, [X21],#4
LOAD:0000000000BEC738                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC73C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC740                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC744                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC748                 BR              X8
LOAD:0000000000BEC74C ; ---------------------------------------------------------------------------
LOAD:0000000000BEC74C
LOAD:0000000000BEC74C loc_BEC74C                              ; CODE XREF: LOAD:0000000000BEC718↑j
LOAD:0000000000BEC74C                                         ; LOAD:0000000000BEC720↑j
LOAD:0000000000BEC74C                 LDR             D0, [X20,X28,LSL#3]
LOAD:0000000000BEC750                 LDR             D1, [X19,X17,LSL#3]
LOAD:0000000000BEC754                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC758                 B.LS            loc_BEE374
LOAD:0000000000BEC75C                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC760                 B.LS            loc_BEE374
LOAD:0000000000BEC764                 FDIV            D2, D0, D1
LOAD:0000000000BEC768                 FRINTM          D2, D2
LOAD:0000000000BEC76C                 FMSUB           D0, D2, D1, D0
LOAD:0000000000BEC770                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC774                 B               loc_BEC734
LOAD:0000000000BEC778 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC778
LOAD:0000000000BEC778 BC_ADDVV
LOAD:0000000000BEC778                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC77C                 AND             X28, X28, #0xFF
LOAD:0000000000BEC780                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC784                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEC788                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC78C                 B.NE            loc_BEC7C0
LOAD:0000000000BEC790                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC794                 B.NE            loc_BEC7C0
LOAD:0000000000BEC798                 ADDS            W0, W0, W1
LOAD:0000000000BEC79C                 B.VS            loc_BEE38C
LOAD:0000000000BEC7A0                 ADD             X0, X0, X24
LOAD:0000000000BEC7A4                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC7A8
LOAD:0000000000BEC7A8 loc_BEC7A8                              ; CODE XREF: LOAD:0000000000BEC7E0↓j
LOAD:0000000000BEC7A8                 LDR             W16, [X21],#4
LOAD:0000000000BEC7AC                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC7B0                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC7B4                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC7B8                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC7BC                 BR              X8
LOAD:0000000000BEC7C0 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC7C0
LOAD:0000000000BEC7C0 loc_BEC7C0                              ; CODE XREF: LOAD:0000000000BEC78C↑j
LOAD:0000000000BEC7C0                                         ; LOAD:0000000000BEC794↑j
LOAD:0000000000BEC7C0                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC7C4                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEC7C8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC7CC                 B.LS            loc_BEE38C
LOAD:0000000000BEC7D0                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC7D4                 B.LS            loc_BEE38C
LOAD:0000000000BEC7D8                 FADD            D0, D0, D1
LOAD:0000000000BEC7DC                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC7E0                 B               loc_BEC7A8
LOAD:0000000000BEC7E4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC7E4
LOAD:0000000000BEC7E4 BC_SUBVV
LOAD:0000000000BEC7E4                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC7E8                 AND             X28, X28, #0xFF
LOAD:0000000000BEC7EC                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC7F0                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEC7F4                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC7F8                 B.NE            loc_BEC82C
LOAD:0000000000BEC7FC                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC800                 B.NE            loc_BEC82C
LOAD:0000000000BEC804                 SUBS            W0, W0, W1
LOAD:0000000000BEC808                 B.VS            loc_BEE38C
LOAD:0000000000BEC80C                 ADD             X0, X0, X24
LOAD:0000000000BEC810                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC814
LOAD:0000000000BEC814 loc_BEC814                              ; CODE XREF: LOAD:0000000000BEC84C↓j
LOAD:0000000000BEC814                 LDR             W16, [X21],#4
LOAD:0000000000BEC818                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC81C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC820                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC824                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC828                 BR              X8
LOAD:0000000000BEC82C ; ---------------------------------------------------------------------------
LOAD:0000000000BEC82C
LOAD:0000000000BEC82C loc_BEC82C                              ; CODE XREF: LOAD:0000000000BEC7F8↑j
LOAD:0000000000BEC82C                                         ; LOAD:0000000000BEC800↑j
LOAD:0000000000BEC82C                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC830                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEC834                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC838                 B.LS            loc_BEE38C
LOAD:0000000000BEC83C                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC840                 B.LS            loc_BEE38C
LOAD:0000000000BEC844                 FSUB            D0, D0, D1
LOAD:0000000000BEC848                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC84C                 B               loc_BEC814
LOAD:0000000000BEC850 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC850
LOAD:0000000000BEC850 BC_MULVV
LOAD:0000000000BEC850                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC854                 AND             X28, X28, #0xFF
LOAD:0000000000BEC858                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC85C                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEC860                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC864                 B.NE            loc_BEC8A0
LOAD:0000000000BEC868                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC86C                 B.NE            loc_BEC8A0
LOAD:0000000000BEC870                 SMULL           X0, W0, W1
LOAD:0000000000BEC874                 CMP             X0, W0,SXTW
LOAD:0000000000BEC878                 MOV             W0, W0
LOAD:0000000000BEC87C                 B.NE            loc_BEE38C
LOAD:0000000000BEC880                 ADD             X0, X0, X24
LOAD:0000000000BEC884                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC888
LOAD:0000000000BEC888 loc_BEC888                              ; CODE XREF: LOAD:0000000000BEC8C0↓j
LOAD:0000000000BEC888                 LDR             W16, [X21],#4
LOAD:0000000000BEC88C                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC890                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC894                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC898                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC89C                 BR              X8
LOAD:0000000000BEC8A0 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC8A0
LOAD:0000000000BEC8A0 loc_BEC8A0                              ; CODE XREF: LOAD:0000000000BEC864↑j
LOAD:0000000000BEC8A0                                         ; LOAD:0000000000BEC86C↑j
LOAD:0000000000BEC8A0                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC8A4                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEC8A8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC8AC                 B.LS            loc_BEE38C
LOAD:0000000000BEC8B0                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC8B4                 B.LS            loc_BEE38C
LOAD:0000000000BEC8B8                 FMUL            D0, D0, D1
LOAD:0000000000BEC8BC                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC8C0                 B               loc_BEC888
LOAD:0000000000BEC8C4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC8C4
LOAD:0000000000BEC8C4 BC_DIVVV
LOAD:0000000000BEC8C4                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC8C8                 AND             X28, X28, #0xFF
LOAD:0000000000BEC8CC                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC8D0                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEC8D4                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC8D8                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEC8DC                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC8E0                 B.LS            loc_BEE38C
LOAD:0000000000BEC8E4                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC8E8                 B.LS            loc_BEE38C
LOAD:0000000000BEC8EC                 FDIV            D0, D0, D1
LOAD:0000000000BEC8F0                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC8F4                 LDR             W16, [X21],#4
LOAD:0000000000BEC8F8                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC8FC                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC900                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC904                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC908                 BR              X8
LOAD:0000000000BEC90C ; ---------------------------------------------------------------------------
LOAD:0000000000BEC90C
LOAD:0000000000BEC90C BC_MODVV
LOAD:0000000000BEC90C                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC910                 AND             X28, X28, #0xFF
LOAD:0000000000BEC914                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC918                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEC91C                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC920                 B.NE            loc_BEC954
LOAD:0000000000BEC924                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC928                 B.NE            loc_BEC954
LOAD:0000000000BEC92C                 CBZ             W1, loc_BEE38C
LOAD:0000000000BEC930                 BL              sub_BEF7FC
LOAD:0000000000BEC934                 ADD             X0, X0, X24
LOAD:0000000000BEC938                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC93C
LOAD:0000000000BEC93C loc_BEC93C                              ; CODE XREF: LOAD:0000000000BEC97C↓j
LOAD:0000000000BEC93C                 LDR             W16, [X21],#4
LOAD:0000000000BEC940                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC944                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC948                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC94C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC950                 BR              X8
LOAD:0000000000BEC954 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC954
LOAD:0000000000BEC954 loc_BEC954                              ; CODE XREF: LOAD:0000000000BEC920↑j
LOAD:0000000000BEC954                                         ; LOAD:0000000000BEC928↑j
LOAD:0000000000BEC954                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC958                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEC95C                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC960                 B.LS            loc_BEE38C
LOAD:0000000000BEC964                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC968                 B.LS            loc_BEE38C
LOAD:0000000000BEC96C                 FDIV            D2, D0, D1
LOAD:0000000000BEC970                 FRINTM          D2, D2
LOAD:0000000000BEC974                 FMSUB           D0, D2, D1, D0
LOAD:0000000000BEC978                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC97C                 B               loc_BEC93C
LOAD:0000000000BEC980 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC980
LOAD:0000000000BEC980 BC_POW
LOAD:0000000000BEC980                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC984                 AND             X28, X28, #0xFF
LOAD:0000000000BEC988                 LDR             X0, [X19,X17,LSL#3]
LOAD:0000000000BEC98C                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEC990                 LDR             D0, [X19,X17,LSL#3]
LOAD:0000000000BEC994                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEC998                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC99C                 B.LS            loc_BEE38C
LOAD:0000000000BEC9A0                 CMP             X25, X1,LSR#32
LOAD:0000000000BEC9A4                 B.LS            loc_BEE38C
LOAD:0000000000BEC9A8                 BL              sub_62F5D0
LOAD:0000000000BEC9AC                 STR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC9B0                 LDR             W16, [X21],#4
LOAD:0000000000BEC9B4                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC9B8                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC9BC                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC9C0                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC9C4                 BR              X8
LOAD:0000000000BEC9C8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC9C8
LOAD:0000000000BEC9C8 BC_CAT
LOAD:0000000000BEC9C8                 UBFX            X17, X16, #0x18, #8
LOAD:0000000000BEC9CC                 AND             X28, X28, #0xFF
LOAD:0000000000BEC9D0                 STR             X19, [X23,#0x20]
LOAD:0000000000BEC9D4                 SUB             X2, X28, X17
LOAD:0000000000BEC9D8                 ADD             X1, X19, X28,LSL#3