// LuaJIT Opcode提取脚本
// 用于从libcocos2dlua.so中提取实际的opcode处理例程地址

var output = false;
var lib_base = null;
var hookInstalled = false;

const moduleName = "libcocos2dlua.so";

function findModule() {
    try {
        if (typeof Module !== 'undefined' && Module.findBaseAddress) {
            lib_base = Module.findBaseAddress(moduleName);
        }
        if (!lib_base) {
            Process.enumerateModules().forEach(function(module) {
                if (module.name === moduleName) {
                    lib_base = module.base;
                    console.log("[*] 找到模块: " + moduleName + " at " + lib_base);
                }
            });
        }
        return lib_base !== null;
    } catch (e) {
        console.log("[!] 查找模块错误: " + e.message);
        return false;
    }
}

// 延迟查找模块，给游戏时间加载
console.log("[*] 等待4秒让游戏加载模块...");
setTimeout(function() {
    console.log("[*] 开始查找模块...");

    var retryCount = 0;
    var maxRetries = 5;

    function tryFindModule() {
        retryCount++;
        console.log("[*] 尝试 " + retryCount + "/" + maxRetries + " 查找模块...");

        if (findModule()) {
            console.log("[*] 成功找到模块: " + lib_base);
            installHook();
        } else {
            if (retryCount < maxRetries) {
                console.log("[*] 模块未找到，2秒后重试...");
                setTimeout(tryFindModule, 2000);
            } else {
                console.log("[!] 模块 " + moduleName + " 未找到，列出所有已加载的模块:");
                Process.enumerateModules().forEach(function(module) {
                    console.log("  " + module.name + " at " + module.base);
                });
            }
        }
    }

    tryFindModule();
}, 4000);

// 标准LuaJIT 2.1.0-beta3 opcode名称表
var opcode_names = [
    "ISLT", "ISGE", "ISLE", "ISGT", "ISEQV", "ISNEV", "ISEQS", "ISNES",
    "ISEQN", "ISNEN", "ISEQP", "ISNEP", "ISTC", "ISFC", "IST", "ISF",
    "ISTYPE", "ISNUM", "MOV", "NOT", "UNM", "LEN", "ADDVN", "SUBVN",
    "MULVN", "DIVVN", "MODVN", "ADDNV", "SUBNV", "MULNV", "DIVNV", "MODNV",
    "ADDVV", "SUBVV", "MULVV", "DIVVV", "MODVV", "POW", "CAT", "KSTR",
    "KCDATA", "KSHORT", "KNUM", "KPRI", "KNIL", "UGET", "USETV", "USETS",
    "USETN", "USETP", "UCLO", "FNEW", "TNEW", "TDUP", "GGET", "GSET",
    "TGETV", "TGETS", "TGETB", "TGETR", "TSETV", "TSETS", "TSETB", "TSETM",
    "TSETR", "CALLM", "CALL", "CALLMT", "CALLT", "ITERC", "ITERN", "VARG",
    "ISNEXT", "RETM", "RET", "RET0", "RET1", "FORI", "JFORI", "FORL",
    "IFORL", "JFORL", "ITERL", "IITERL", "JITERL", "LOOP", "ILOOP", "JLOOP",
    "JMP", "FUNCF", "IFUNCF", "JFUNCF", "FUNCV", "IFUNCV", "JFUNCV", "FUNCC",
    "FUNCCW"
];

// Hook安装函数
function installHook() {
    if (hookInstalled) {
        console.log("[*] Hook已经安装，跳过");
        return;
    }

    const offset = 0xBEDFD4; // 原始偏移
    const targetAddr = lib_base.add(offset);
    console.log("[*] 尝试Hook地址: " + targetAddr);

    try {
        Interceptor.attach(targetAddr, {
            onEnter: function() {
                if (!output) {
                    console.log("[+] Hook triggered, extracting opcodes...");

                    try {
                        var GL = this.context.x22;
                        if (!GL) {
                            console.log("[!] 无法获取GL寄存器，尝试其他寄存器...");
                            // 尝试其他可能的寄存器
                            GL = this.context.x21 || this.context.x20 || this.context.x19;
                        }

                        if (!GL) {
                            console.log("[!] 无法获取全局状态指针");
                            return;
                        }

                        var dispatch = GL.add(0xF70);

                        console.log("[+] GL address: " + GL);
                        console.log("[+] Dispatch table address: " + dispatch);
                        console.log("");
                        console.log("=== LuaJIT Opcode Analysis ===");
                        console.log("Index | Opcode Name | Handler Address | Offset from Base");
                        console.log("------|-------------|-----------------|------------------");

                        var opcodes_info = [];

                        for (var i = 0; i < 97; ++i) {
                            try {
                                var prog_ptr = dispatch.add(i * 8).readPointer();
                                var offset = prog_ptr.sub(lib_base);
                                var opcode_name = (i < opcode_names.length) ? opcode_names[i] : "UNKNOWN_" + i;

                                console.log(sprintf("%5d | %11s | %15s | 0x%08x",
                                    i, opcode_name, prog_ptr, offset.toInt32()));

                                opcodes_info.push({
                                    index: i,
                                    name: opcode_name,
                                    handler: prog_ptr,
                                    offset: offset.toInt32()
                                });
                            } catch (e) {
                                console.log("[!] 读取opcode " + i + " 失败: " + e);
                            }
                        }
                    } catch (e) {
                        console.log("[!] Hook执行失败: " + e);
                    }

                    console.log("");
                    console.log("=== 不支持的指令分析 ===");

                    // 检查我们反编译器不支持的指令
                    var unsupported_opcodes = [
                        17, 18, // ISTYPE, ISNUM
                        63, 68, // TGETR, TSETR
                        82, 84, 85, 87, 88, 90, 91, // JIT相关指令
                        93, 94, 95, 96, 97, 98, 99, 100 // 函数相关指令
                    ];

                    console.log("以下指令在当前反编译器中不被支持：");
                    for (var j = 0; j < unsupported_opcodes.length; j++) {
                        var idx = unsupported_opcodes[j];
                        if (idx < opcodes_info.length) {
                            var info = opcodes_info[idx];
                            console.log("  [" + info.index + "] " + info.name + " -> 0x" +
                                info.offset.toString(16).padStart(8, '0').toUpperCase());
                        }
                    }

                    console.log("");
                    console.log("=== 建议的修复方案 ===");
                    console.log("1. 在反编译器中添加对上述指令的支持");
                    console.log("2. 将JIT指令映射到对应的基础指令");
                    console.log("3. 为函数调用指令添加通用处理逻辑");

                    output = true;
                }
        },
        onLeave: function() {}
    });

        hookInstalled = true;
        console.log("[*] Hook安装成功！");

    } catch (e) {
        console.log("[!] Hook失败: " + e.message);
    }
}

// 辅助函数：格式化输出
function sprintf(format, index, name, address, offset) {
    return format.replace(/%5d/g, index.toString().padStart(5, ' '))
                 .replace(/%11s/g, name.toString().padEnd(11, ' '))
                 .replace(/%15s/g, address.toString())
                 .replace(/0x%08x/g, '0x' + offset.toString(16).padStart(8, '0').toUpperCase());
}

console.log("[+] LuaJIT Opcode提取脚本已加载");
console.log("[+] 将在4秒后开始查找模块...");
console.log("[+] 请确保游戏已完全启动并加载了Lua模块");
