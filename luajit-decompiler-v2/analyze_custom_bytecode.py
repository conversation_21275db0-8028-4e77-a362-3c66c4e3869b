#!/usr/bin/env python3
"""
自定义LuaJIT字节码分析器
基于从运行时提取的opcode信息分析字节码文件
"""

import sys
import struct
from collections import Counter

# 标准LuaJIT 2.1.0-beta3 opcode名称
STANDARD_OPCODES = [
    "ISLT", "ISGE", "ISLE", "ISGT", "ISEQV", "ISNEV", "ISEQS", "ISNES",
    "ISEQN", "ISNEN", "ISEQP", "ISNEP", "ISTC", "ISFC", "IST", "ISF",
    "ISTYPE", "ISNUM", "MOV", "NOT", "UNM", "LEN", "ADDVN", "SUBVN",
    "MULVN", "DIVVN", "MODVN", "ADDNV", "SUBNV", "MULNV", "DIVNV", "MODNV",
    "ADDVV", "SUBVV", "MULVV", "DIVVV", "MODVV", "POW", "CAT", "KSTR",
    "KCDA<PERSON>", "KSHORT", "KNUM", "KPRI", "KNIL", "UGET", "USETV", "USETS",
    "USETN", "USETP", "UCLO", "FNEW", "TNEW", "TDUP", "GGET", "GSET",
    "TGETV", "TGETS", "TGETB", "TGETR", "TSETV", "TSETS", "TSETB", "TSETM",
    "TSETR", "CALLM", "CALL", "CALLMT", "CALLT", "ITERC", "ITERN", "VARG",
    "ISNEXT", "RETM", "RET", "RET0", "RET1", "FORI", "JFORI", "FORL",
    "IFORL", "JFORL", "ITERL", "IITERL", "JITERL", "LOOP", "ILOOP", "JLOOP",
    "JMP", "FUNCF", "IFUNCF", "JFUNCF", "FUNCV", "IFUNCV", "JFUNCV", "FUNCC",
    "FUNCCW"
]

# 我们反编译器当前不支持的指令
UNSUPPORTED_OPCODES = {
    17: "ISTYPE",
    18: "ISNUM", 
    63: "TGETR",
    68: "TSETR",
    82: "JFORI",
    84: "IFORL", 
    85: "JFORL",
    87: "IITERL",
    88: "JITERL", 
    90: "ILOOP",
    91: "JLOOP",
    93: "FUNCF",
    94: "IFUNCF",
    95: "JFUNCF", 
    96: "FUNCV",
    97: "IFUNCV",
    98: "JFUNCV",
    99: "FUNCC",
    100: "FUNCCW"
}

def analyze_bytecode_file(filename, custom_opcodes=None):
    """分析字节码文件，使用自定义或标准opcode映射"""
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
    except Exception as e:
        print(f"错误: 无法读取文件 {filename}: {e}")
        return None
    
    print(f"=== 分析文件: {filename} ===")
    print(f"文件大小: {len(data)} 字节")
    
    # 检查LuaJIT魔数
    if len(data) < 4 or data[:3] != b'\x1bLJ':
        print("错误: 不是有效的LuaJIT字节码文件")
        return None
    
    version = data[3]
    flags = data[4] if len(data) > 4 else 0
    
    print(f"LuaJIT版本: {version}")
    print(f"标志: 0x{flags:02x}")
    print()
    
    # 分析指令
    instructions = []
    unsupported_instructions = []
    
    # 从偏移16开始扫描指令
    i = 16
    while i < len(data) - 4:
        opcode = data[i]
        instructions.append(opcode)
        
        # 检查是否为不支持的指令
        if opcode in UNSUPPORTED_OPCODES:
            context = data[max(0, i-4):i+8].hex()
            unsupported_instructions.append({
                'offset': i,
                'opcode': opcode,
                'name': UNSUPPORTED_OPCODES[opcode],
                'context': context
            })
        
        i += 4  # 大多数指令是4字节
    
    # 统计指令
    instruction_counts = Counter(instructions)
    
    print("=== 指令统计 ===")
    print(f"总指令数: {len(instructions)}")
    print(f"不支持的指令数: {len(unsupported_instructions)}")
    print()
    
    if unsupported_instructions:
        print("=== 不支持的指令详情 ===")
        for instr in unsupported_instructions:
            print(f"偏移 0x{instr['offset']:04x}: {instr['name']} (0x{instr['opcode']:02x})")
            print(f"  上下文: {instr['context']}")
        print()
    
    # 显示最常见的指令
    print("=== 最常见的指令 (前15个) ===")
    for opcode, count in instruction_counts.most_common(15):
        opcode_name = STANDARD_OPCODES[opcode] if opcode < len(STANDARD_OPCODES) else f"UNK_{opcode}"
        supported = "✓" if opcode not in UNSUPPORTED_OPCODES else "✗"
        print(f"{supported} 0x{opcode:02x} {opcode_name:10s}: {count:3d} 次")
    print()
    
    # 生成修复建议
    if unsupported_instructions:
        print("=== 修复建议 ===")
        unsupported_types = {}
        for instr in unsupported_instructions:
            opcode = instr['opcode']
            name = instr['name']
            
            if name.startswith('J') or name.startswith('I'):
                category = "JIT优化指令"
            elif 'FUNC' in name:
                category = "函数调用指令"
            elif name in ['TGETR', 'TSETR']:
                category = "表操作指令"
            else:
                category = "其他指令"
            
            if category not in unsupported_types:
                unsupported_types[category] = []
            unsupported_types[category].append(name)
        
        for category, opcodes in unsupported_types.items():
            print(f"\n{category}:")
            unique_opcodes = list(set(opcodes))
            for opcode in unique_opcodes:
                count = sum(1 for instr in unsupported_instructions if instr['name'] == opcode)
                print(f"  - {opcode}: {count} 次")
        
        print("\n建议的处理方法:")
        print("1. JIT优化指令 -> 映射到对应的基础指令")
        print("2. 函数调用指令 -> 统一处理为CALL指令")
        print("3. 表操作指令 -> 扩展现有的表操作逻辑")
    
    return {
        'total_instructions': len(instructions),
        'unsupported_count': len(unsupported_instructions),
        'unsupported_details': unsupported_instructions,
        'instruction_counts': instruction_counts
    }

def load_custom_opcodes(frida_output_file):
    """从Frida输出文件加载自定义opcode映射"""
    # 这里可以解析Frida脚本的输出
    # 暂时返回None，使用标准映射
    return None

def main():
    if len(sys.argv) < 2:
        print("用法: python3 analyze_custom_bytecode.py <ljbc文件> [frida_output]")
        print("示例:")
        print("  python3 analyze_custom_bytecode.py app_module_dfdc_DfdcModule.ljbc")
        print("  python3 analyze_custom_bytecode.py app_module_dfdc_DfdcModule.ljbc opcodes.txt")
        sys.exit(1)
    
    bytecode_file = sys.argv[1]
    frida_output = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 加载自定义opcode映射（如果有）
    custom_opcodes = None
    if frida_output:
        custom_opcodes = load_custom_opcodes(frida_output)
        if custom_opcodes:
            print(f"已加载自定义opcode映射: {frida_output}")
        else:
            print(f"警告: 无法加载自定义映射，使用标准映射")
    
    # 分析字节码文件
    result = analyze_bytecode_file(bytecode_file, custom_opcodes)
    
    if result:
        print("\n=== 分析完成 ===")
        if result['unsupported_count'] == 0:
            print("✓ 所有指令都被支持，可以直接反编译")
        else:
            print(f"✗ 发现 {result['unsupported_count']} 个不支持的指令")
            print("  需要扩展反编译器或使用其他工具")

if __name__ == "__main__":
    main()
