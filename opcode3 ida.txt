LOAD:0000000000BEBC10                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEBC14                 LDRH            W17, [X21,#2]
LOAD:0000000000BEBC18                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEBC1C                 ADD             X21, X21, #4
LOAD:0000000000BEBC20                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEBC24                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEBC28                 CMP             X25, X0,LSR#32
LOAD:0000000000BEBC2C                 B.NE            loc_BEBC58
LOAD:0000000000BEBC30                 CMP             X25, X1,LSR#32
LOAD:0000000000BEBC34                 B.NE            loc_BEBC78
LOAD:0000000000BEBC38                 CMP             W0, W1
LOAD:0000000000BEBC3C                 CSEL            X21, X17, X21, LT
LOAD:0000000000BEBC40
LOAD:0000000000BEBC40 loc_BEBC40                              ; CODE XREF: BC_ISLT+7C↓j
LOAD:0000000000BEBC40                 LDR             W16, [X21],#4
LOAD:0000000000BEBC44                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEBC48                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEBC4C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEBC50                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEBC54                 BR              X8
LOAD:0000000000BEBC58 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBC58
LOAD:0000000000BEBC58 loc_BEBC58                              ; CODE XREF: BC_ISLT+1C↑j
LOAD:0000000000BEBC58                 LDR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEBC5C                 B.CC            loc_BEE288
LOAD:0000000000BEBC60                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEBC64                 CMP             X25, X1,LSR#32
LOAD:0000000000BEBC68                 B.HI            loc_BEBC84
LOAD:0000000000BEBC6C                 B.NE            loc_BEE288
LOAD:0000000000BEBC70                 SCVTF           D1, W1
LOAD:0000000000BEBC74                 B               loc_BEBC84
LOAD:0000000000BEBC78 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBC78
LOAD:0000000000BEBC78 loc_BEBC78                              ; CODE XREF: BC_ISLT+24↑j
LOAD:0000000000BEBC78                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEBC7C                 B.CC            loc_BEE288
LOAD:0000000000BEBC80                 SCVTF           D0, W0
LOAD:0000000000BEBC84
LOAD:0000000000BEBC84 loc_BEBC84                              ; CODE XREF: BC_ISLT+58↑j
LOAD:0000000000BEBC84                                         ; BC_ISLT+64↑j
LOAD:0000000000BEBC84                 FCMP            D0, D1
LOAD:0000000000BEBC88                 CSEL            X21, X17, X21, CC
LOAD:0000000000BEBC8C                 B               loc_BEBC40
LOAD:0000000000BEBC8C ; End of function BC_ISLT
LOAD:0000000000BEBC8C
LOAD:0000000000BEBC90
LOAD:0000000000BEBC90 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEBC90
LOAD:0000000000BEBC90
LOAD:0000000000BEBC90 ; __int64 __fastcall BC_ISGE(long double)
LOAD:0000000000BEBC90 BC_ISGE
LOAD:0000000000BEBC90
LOAD:0000000000BEBC90 ; FUNCTION CHUNK AT LOAD:0000000000BEE288 SIZE 00000054 BYTES
LOAD:0000000000BEBC90
LOAD:0000000000BEBC90                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEBC94                 LDRH            W17, [X21,#2]
LOAD:0000000000BEBC98                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEBC9C                 ADD             X21, X21, #4
LOAD:0000000000BEBCA0                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEBCA4                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEBCA8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEBCAC                 B.NE            loc_BEBCD8
LOAD:0000000000BEBCB0                 CMP             X25, X1,LSR#32
LOAD:0000000000BEBCB4                 B.NE            loc_BEBCF8
LOAD:0000000000BEBCB8                 CMP             W0, W1
LOAD:0000000000BEBCBC                 CSEL            X21, X17, X21, GE
LOAD:0000000000BEBCC0
LOAD:0000000000BEBCC0 loc_BEBCC0                              ; CODE XREF: BC_ISGE+7C↓j
LOAD:0000000000BEBCC0                 LDR             W16, [X21],#4
LOAD:0000000000BEBCC4                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEBCC8                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEBCCC                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEBCD0                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEBCD4                 BR              X8
LOAD:0000000000BEBCD8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBCD8
LOAD:0000000000BEBCD8 loc_BEBCD8                              ; CODE XREF: BC_ISGE+1C↑j
LOAD:0000000000BEBCD8                 LDR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEBCDC                 B.CC            loc_BEE288
LOAD:0000000000BEBCE0                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEBCE4                 CMP             X25, X1,LSR#32
LOAD:0000000000BEBCE8                 B.HI            loc_BEBD04
LOAD:0000000000BEBCEC                 B.NE            loc_BEE288
LOAD:0000000000BEBCF0                 SCVTF           D1, W1
LOAD:0000000000BEBCF4                 B               loc_BEBD04
LOAD:0000000000BEBCF8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBCF8
LOAD:0000000000BEBCF8 loc_BEBCF8                              ; CODE XREF: BC_ISGE+24↑j
LOAD:0000000000BEBCF8                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEBCFC                 B.CC            loc_BEE288
LOAD:0000000000BEBD00                 SCVTF           D0, W0
LOAD:0000000000BEBD04
LOAD:0000000000BEBD04 loc_BEBD04                              ; CODE XREF: BC_ISGE+58↑j
LOAD:0000000000BEBD04                                         ; BC_ISGE+64↑j
LOAD:0000000000BEBD04                 FCMP            D0, D1
LOAD:0000000000BEBD08                 CSEL            X21, X17, X21, CS
LOAD:0000000000BEBD0C                 B               loc_BEBCC0
LOAD:0000000000BEBD0C ; End of function BC_ISGE
LOAD:0000000000BEBD0C
LOAD:0000000000BEBD10
LOAD:0000000000BEBD10 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEBD10
LOAD:0000000000BEBD10
LOAD:0000000000BEBD10 ; __int64 __fastcall BC_ISLE(long double)
LOAD:0000000000BEBD10 BC_ISLE
LOAD:0000000000BEBD10
LOAD:0000000000BEBD10 ; FUNCTION CHUNK AT LOAD:0000000000BEE288 SIZE 00000054 BYTES
LOAD:0000000000BEBD10
LOAD:0000000000BEBD10                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEBD14                 LDRH            W17, [X21,#2]
LOAD:0000000000BEBD18                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEBD1C                 ADD             X21, X21, #4
LOAD:0000000000BEBD20                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEBD24                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEBD28                 CMP             X25, X0,LSR#32
LOAD:0000000000BEBD2C                 B.NE            loc_BEBD58
LOAD:0000000000BEBD30                 CMP             X25, X1,LSR#32
LOAD:0000000000BEBD34                 B.NE            loc_BEBD78
LOAD:0000000000BEBD38                 CMP             W0, W1
LOAD:0000000000BEBD3C                 CSEL            X21, X17, X21, LE
LOAD:0000000000BEBD40
LOAD:0000000000BEBD40 loc_BEBD40                              ; CODE XREF: BC_ISLE+7C↓j
LOAD:0000000000BEBD40                 LDR             W16, [X21],#4
LOAD:0000000000BEBD44                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEBD48                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEBD4C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEBD50                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEBD54                 BR              X8
LOAD:0000000000BEBD58 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBD58
LOAD:0000000000BEBD58 loc_BEBD58                              ; CODE XREF: BC_ISLE+1C↑j
LOAD:0000000000BEBD58                 LDR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEBD5C                 B.CC            loc_BEE288
LOAD:0000000000BEBD60                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEBD64                 CMP             X25, X1,LSR#32
LOAD:0000000000BEBD68                 B.HI            loc_BEBD84
LOAD:0000000000BEBD6C                 B.NE            loc_BEE288
LOAD:0000000000BEBD70                 SCVTF           D1, W1
LOAD:0000000000BEBD74                 B               loc_BEBD84
LOAD:0000000000BEBD78 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBD78
LOAD:0000000000BEBD78 loc_BEBD78                              ; CODE XREF: BC_ISLE+24↑j
LOAD:0000000000BEBD78                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEBD7C                 B.CC            loc_BEE288
LOAD:0000000000BEBD80                 SCVTF           D0, W0
LOAD:0000000000BEBD84
LOAD:0000000000BEBD84 loc_BEBD84                              ; CODE XREF: BC_ISLE+58↑j
LOAD:0000000000BEBD84                                         ; BC_ISLE+64↑j
LOAD:0000000000BEBD84                 FCMP            D0, D1
LOAD:0000000000BEBD88                 CSEL            X21, X17, X21, LS
LOAD:0000000000BEBD8C                 B               loc_BEBD40
LOAD:0000000000BEBD8C ; End of function BC_ISLE
LOAD:0000000000BEBD8C
LOAD:0000000000BEBD90
LOAD:0000000000BEBD90 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEBD90
LOAD:0000000000BEBD90
LOAD:0000000000BEBD90 ; __int64 __fastcall BC_ISGT(long double)
LOAD:0000000000BEBD90 BC_ISGT
LOAD:0000000000BEBD90
LOAD:0000000000BEBD90 ; FUNCTION CHUNK AT LOAD:0000000000BEE288 SIZE 00000054 BYTES
LOAD:0000000000BEBD90
LOAD:0000000000BEBD90                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEBD94                 LDRH            W17, [X21,#2]
LOAD:0000000000BEBD98                 LDR             X1, [X19,X28,LSL#3]
LOAD:0000000000BEBD9C                 ADD             X21, X21, #4
LOAD:0000000000BEBDA0                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEBDA4                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEBDA8                 CMP             X25, X0,LSR#32
LOAD:0000000000BEBDAC                 B.NE            loc_BEBDD8
LOAD:0000000000BEBDB0                 CMP             X25, X1,LSR#32
LOAD:0000000000BEBDB4                 B.NE            loc_BEBDF8
LOAD:0000000000BEBDB8                 CMP             W0, W1
LOAD:0000000000BEBDBC                 CSEL            X21, X17, X21, GT
LOAD:0000000000BEBDC0
LOAD:0000000000BEBDC0 loc_BEBDC0                              ; CODE XREF: BC_ISGT+7C↓j
LOAD:0000000000BEBDC0                 LDR             W16, [X21],#4
LOAD:0000000000BEBDC4                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEBDC8                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEBDCC                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEBDD0                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEBDD4                 BR              X8
LOAD:0000000000BEBDD8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBDD8
LOAD:0000000000BEBDD8 loc_BEBDD8                              ; CODE XREF: BC_ISGT+1C↑j
LOAD:0000000000BEBDD8                 LDR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEBDDC                 B.CC            loc_BEE288
LOAD:0000000000BEBDE0                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEBDE4                 CMP             X25, X1,LSR#32
LOAD:0000000000BEBDE8                 B.HI            loc_BEBE04
LOAD:0000000000BEBDEC                 B.NE            loc_BEE288
LOAD:0000000000BEBDF0                 SCVTF           D1, W1
LOAD:0000000000BEBDF4                 B               loc_BEBE04
LOAD:0000000000BEBDF8 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBDF8
LOAD:0000000000BEBDF8 loc_BEBDF8                              ; CODE XREF: BC_ISGT+24↑j
LOAD:0000000000BEBDF8                 LDR             D1, [X19,X28,LSL#3]
LOAD:0000000000BEBDFC                 B.CC            loc_BEE288
LOAD:0000000000BEBE00                 SCVTF           D0, W0
LOAD:0000000000BEBE04
LOAD:0000000000BEBE04 loc_BEBE04                              ; CODE XREF: BC_ISGT+58↑j
LOAD:0000000000BEBE04                                         ; BC_ISGT+64↑j
LOAD:0000000000BEBE04                 FCMP            D0, D1
LOAD:0000000000BEBE08                 CSEL            X21, X17, X21, HI
LOAD:0000000000BEBE0C                 B               loc_BEBDC0
LOAD:0000000000BEBE0C ; End of function BC_ISGT
LOAD:0000000000BEBE0C
LOAD:0000000000BEBE10
LOAD:0000000000BEBE10 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEBE10
LOAD:0000000000BEBE10
LOAD:0000000000BEBE10 ; __int64 BC_ISEQV()
LOAD:0000000000BEBE10 BC_ISEQV
LOAD:0000000000BEBE10
LOAD:0000000000BEBE10 arg_8           =  8
LOAD:0000000000BEBE10
LOAD:0000000000BEBE10 ; FUNCTION CHUNK AT LOAD:0000000000BEC05C SIZE 00000070 BYTES
LOAD:0000000000BEBE10 ; FUNCTION CHUNK AT LOAD:0000000000BEE310 SIZE 00000038 BYTES
LOAD:0000000000BEBE10
LOAD:0000000000BEBE10                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEBE14                 ADD             X28, X19, X28,LSL#3
LOAD:0000000000BEBE18                 LDRH            W17, [X21,#2]
LOAD:0000000000BEBE1C                 LDR             X2, [X28]
LOAD:0000000000BEBE20                 ADD             X21, X21, #4
LOAD:0000000000BEBE24                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEBE28                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEBE2C                 ASR             X15, X2, #0x2F ; '/'
LOAD:0000000000BEBE30                 CMN             X15, #0xE
LOAD:0000000000BEBE34                 B.LS            loc_BEBFD0
LOAD:0000000000BEBE38                 ASR             X8, X0, #0x2F ; '/'
LOAD:0000000000BEBE3C                 CMN             X15, #0xB
LOAD:0000000000BEBE40                 CCMN            X8, #0xB, #4, NE
LOAD:0000000000BEBE44                 B.EQ            loc_BEE32C
LOAD:0000000000BEBE48                 CMP             X0, X2
LOAD:0000000000BEBE4C                 B.NE            loc_BEBE6C
LOAD:0000000000BEBE50
LOAD:0000000000BEBE50 loc_BEBE50                              ; CODE XREF: BC_ISEQV+E4↓j
LOAD:0000000000BEBE50                                         ; BC_ISEQV+F0↓j ...
LOAD:0000000000BEBE50                 MOV             X21, X17
LOAD:0000000000BEBE54
LOAD:0000000000BEBE54 loc_BEBE54                              ; CODE XREF: BC_ISEQV+64↓j
LOAD:0000000000BEBE54                                         ; BC_ISEQV+70↓j ...
LOAD:0000000000BEBE54                 LDR             W16, [X21],#4
LOAD:0000000000BEBE58                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEBE5C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEBE60                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEBE64                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEBE68                 BR              X8
LOAD:0000000000BEBE6C ; ---------------------------------------------------------------------------
LOAD:0000000000BEBE6C
LOAD:0000000000BEBE6C loc_BEBE6C                              ; CODE XREF: BC_ISEQV+3C↑j
LOAD:0000000000BEBE6C                 CMP             X15, X8
LOAD:0000000000BEBE70                 CCMN            X15, #0xC, #2, EQ
LOAD:0000000000BEBE74                 B.HI            loc_BEBE54
LOAD:0000000000BEBE78                 AND             X1, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BEBE7C                 LDR             X10, [X1,#0x20]
LOAD:0000000000BEBE80                 CBZ             X10, loc_BEBE54
LOAD:0000000000BEBE84                 LDRB            W9, [X10,#0xA]
LOAD:0000000000BEBE88                 MOV             W3, #0
LOAD:0000000000BEBE8C                 TBNZ            W9, #4, loc_BEBE54
LOAD:0000000000BEBE90                 B               loc_BEE310
LOAD:0000000000BEBE94 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBE94
LOAD:0000000000BEBE94 BC_ISNEV
LOAD:0000000000BEBE94                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEBE98                 ADD             X28, X19, X28,LSL#3
LOAD:0000000000BEBE9C                 LDRH            W17, [X21,#2]
LOAD:0000000000BEBEA0                 LDR             X2, [X28]
LOAD:0000000000BEBEA4                 ADD             X21, X21, #4
LOAD:0000000000BEBEA8                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEBEAC                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEBEB0                 ASR             X15, X2, #0x2F ; '/'
LOAD:0000000000BEBEB4                 CMN             X15, #0xE
LOAD:0000000000BEBEB8                 B.LS            loc_BEC05C
LOAD:0000000000BEBEBC                 ASR             X8, X0, #0x2F ; '/'
LOAD:0000000000BEBEC0                 CMN             X15, #0xB
LOAD:0000000000BEBEC4                 CCMN            X8, #0xB, #4, NE
LOAD:0000000000BEBEC8                 B.EQ            loc_BEE32C
LOAD:0000000000BEBECC                 CMP             X0, X2
LOAD:0000000000BEBED0                 B.NE            loc_BEBEEC
LOAD:0000000000BEBED4                 LDR             W16, [X21],#4
LOAD:0000000000BEBED8                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEBEDC                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEBEE0                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEBEE4                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEBEE8                 BR              X8
LOAD:0000000000BEBEEC ; ---------------------------------------------------------------------------
LOAD:0000000000BEBEEC
LOAD:0000000000BEBEEC loc_BEBEEC                              ; CODE XREF: BC_ISEQV+C0↑j
LOAD:0000000000BEBEEC                 CMP             X15, X8
LOAD:0000000000BEBEF0                 CCMN            X15, #0xC, #2, EQ
LOAD:0000000000BEBEF4                 B.HI            loc_BEBE50
LOAD:0000000000BEBEF8                 AND             X1, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BEBEFC                 LDR             X10, [X1,#0x20]
LOAD:0000000000BEBF00                 CBZ             X10, loc_BEBE50
LOAD:0000000000BEBF04                 LDRB            W9, [X10,#0xA]
LOAD:0000000000BEBF08                 MOV             W3, #1
LOAD:0000000000BEBF0C                 TBNZ            W9, #4, loc_BEBE50
LOAD:0000000000BEBF10                 B               loc_BEE310
LOAD:0000000000BEBF14 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBF14
LOAD:0000000000BEBF14 BC_ISEQS
LOAD:0000000000BEBF14                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEBF18                 MVN             X28, X28
LOAD:0000000000BEBF1C                 LDRH            W17, [X21,#2]
LOAD:0000000000BEBF20                 LDR             X1, [X20,X28,LSL#3]
LOAD:0000000000BEBF24                 ADD             X21, X21, #4
LOAD:0000000000BEBF28                 MOV             X8, #0xFFFFFFFFFFFFFFFB
LOAD:0000000000BEBF2C                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEBF30                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEBF34                 ADD             X1, X1, X8,LSL#47
LOAD:0000000000BEBF38                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEBF3C                 CMN             X15, #0xB
LOAD:0000000000BEBF40                 B.EQ            loc_BEE32C
LOAD:0000000000BEBF44                 CMP             X0, X1
LOAD:0000000000BEBF48                 CSEL            X21, X17, X21, EQ
LOAD:0000000000BEBF4C                 LDR             W16, [X21],#4
LOAD:0000000000BEBF50                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEBF54                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEBF58                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEBF5C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEBF60                 BR              X8
LOAD:0000000000BEBF64 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBF64
LOAD:0000000000BEBF64 BC_ISNES
LOAD:0000000000BEBF64                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEBF68                 MVN             X28, X28
LOAD:0000000000BEBF6C                 LDRH            W17, [X21,#2]
LOAD:0000000000BEBF70                 LDR             X1, [X20,X28,LSL#3]
LOAD:0000000000BEBF74                 ADD             X21, X21, #4
LOAD:0000000000BEBF78                 MOV             X8, #0xFFFFFFFFFFFFFFFB
LOAD:0000000000BEBF7C                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEBF80                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEBF84                 ADD             X1, X1, X8,LSL#47
LOAD:0000000000BEBF88                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEBF8C                 CMN             X15, #0xB
LOAD:0000000000BEBF90                 B.EQ            loc_BEE32C
LOAD:0000000000BEBF94                 CMP             X0, X1
LOAD:0000000000BEBF98                 CSEL            X21, X17, X21, NE
LOAD:0000000000BEBF9C                 LDR             W16, [X21],#4
LOAD:0000000000BEBFA0                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEBFA4                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEBFA8                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEBFAC                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEBFB0                 BR              X8
LOAD:0000000000BEBFB4 ; ---------------------------------------------------------------------------
LOAD:0000000000BEBFB4
LOAD:0000000000BEBFB4 BC_ISEQN
LOAD:0000000000BEBFB4                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEBFB8                 ADD             X28, X20, X28,LSL#3
LOAD:0000000000BEBFBC                 LDRH            W17, [X21,#2]
LOAD:0000000000BEBFC0                 LDR             X2, [X28]
LOAD:0000000000BEBFC4                 ADD             X21, X21, #4
LOAD:0000000000BEBFC8                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEBFCC                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEBFD0
LOAD:0000000000BEBFD0 loc_BEBFD0                              ; CODE XREF: BC_ISEQV+24↑j
LOAD:0000000000BEBFD0                 CMP             X25, X0,LSR#32
LOAD:0000000000BEBFD4                 B.NE            loc_BEC000
LOAD:0000000000BEBFD8                 CMP             X25, X2,LSR#32
LOAD:0000000000BEBFDC                 B.NE            loc_BEC020
LOAD:0000000000BEBFE0                 CMP             W0, W2
LOAD:0000000000BEBFE4
LOAD:0000000000BEBFE4 loc_BEBFE4                              ; CODE XREF: BC_ISEQV+20C↓j
LOAD:0000000000BEBFE4                                         ; BC_ISEQV+21C↓j
LOAD:0000000000BEBFE4                 CSEL            X21, X17, X21, EQ
LOAD:0000000000BEBFE8
LOAD:0000000000BEBFE8 loc_BEBFE8                              ; CODE XREF: BC_ISEQV+228↓j
LOAD:0000000000BEBFE8                 LDR             W16, [X21],#4
LOAD:0000000000BEBFEC                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEBFF0                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEBFF4                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEBFF8                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEBFFC                 BR              X8
LOAD:0000000000BEC000 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC000
LOAD:0000000000BEC000 loc_BEC000                              ; CODE XREF: BC_ISEQV+1C4↑j
LOAD:0000000000BEC000                 B.CC            loc_BEC030
LOAD:0000000000BEC004                 LDR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC008                 LDR             D1, [X28]
LOAD:0000000000BEC00C                 CMP             X25, X2,LSR#32
LOAD:0000000000BEC010                 B.NE            loc_BEC018
LOAD:0000000000BEC014                 SCVTF           D1, W2
LOAD:0000000000BEC018
LOAD:0000000000BEC018 loc_BEC018                              ; CODE XREF: BC_ISEQV+200↑j
LOAD:0000000000BEC018                 FCMP            D0, D1
LOAD:0000000000BEC01C                 B               loc_BEBFE4
LOAD:0000000000BEC020 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC020
LOAD:0000000000BEC020 loc_BEC020                              ; CODE XREF: BC_ISEQV+1CC↑j
LOAD:0000000000BEC020                 LDR             D1, [X28]
LOAD:0000000000BEC024                 SCVTF           D0, W0
LOAD:0000000000BEC028                 FCMP            D0, D1
LOAD:0000000000BEC02C                 B               loc_BEBFE4
LOAD:0000000000BEC030 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC030
LOAD:0000000000BEC030 loc_BEC030                              ; CODE XREF: BC_ISEQV:loc_BEC000↑j
LOAD:0000000000BEC030                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEC034                 CMN             X15, #0xB
LOAD:0000000000BEC038                 B.NE            loc_BEBFE8
LOAD:0000000000BEC03C                 B               loc_BEE32C
LOAD:0000000000BEC03C ; End of function BC_ISEQV
LOAD:0000000000BEC03C
LOAD:0000000000BEC040
LOAD:0000000000BEC040 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEC040
LOAD:0000000000BEC040
LOAD:0000000000BEC040 ; void BC_ISNEN()
LOAD:0000000000BEC040 BC_ISNEN
LOAD:0000000000BEC040                 LDR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC044                 ADD             X28, X20, X28,LSL#3
LOAD:0000000000BEC048                 LDRH            W17, [X21,#2]
LOAD:0000000000BEC04C                 LDR             X2, [X28]
LOAD:0000000000BEC050                 ADD             X21, X21, #4
LOAD:0000000000BEC054                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEC058                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEC058 ; End of function BC_ISNEN
LOAD:0000000000BEC058
LOAD:0000000000BEC05C ; START OF FUNCTION CHUNK FOR BC_ISEQV
LOAD:0000000000BEC05C
LOAD:0000000000BEC05C loc_BEC05C                              ; CODE XREF: BC_ISEQV+A8↑j
LOAD:0000000000BEC05C                 CMP             X25, X0,LSR#32
LOAD:0000000000BEC060                 B.NE            loc_BEC08C
LOAD:0000000000BEC064                 CMP             X25, X2,LSR#32
LOAD:0000000000BEC068                 B.NE            loc_BEC0AC
LOAD:0000000000BEC06C                 CMP             W0, W2
LOAD:0000000000BEC070
LOAD:0000000000BEC070 loc_BEC070                              ; CODE XREF: BC_ISEQV+298↓j
LOAD:0000000000BEC070                                         ; BC_ISEQV+2A8↓j ...
LOAD:0000000000BEC070                 CSEL            X21, X17, X21, NE
LOAD:0000000000BEC074                 LDR             W16, [X21],#4
LOAD:0000000000BEC078                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC07C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC080                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC084                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC088                 BR              X8
LOAD:0000000000BEC08C ; ---------------------------------------------------------------------------
LOAD:0000000000BEC08C
LOAD:0000000000BEC08C loc_BEC08C                              ; CODE XREF: BC_ISEQV+250↑j
LOAD:0000000000BEC08C                 B.CC            loc_BEC0BC
LOAD:0000000000BEC090                 LDR             D0, [X19,X27,LSL#3]
LOAD:0000000000BEC094                 LDR             D1, [X28]
LOAD:0000000000BEC098                 CMP             X25, X2,LSR#32
LOAD:0000000000BEC09C                 B.NE            loc_BEC0A4
LOAD:0000000000BEC0A0                 SCVTF           D1, W2
LOAD:0000000000BEC0A4
LOAD:0000000000BEC0A4 loc_BEC0A4                              ; CODE XREF: BC_ISEQV+28C↑j
LOAD:0000000000BEC0A4                 FCMP            D0, D1
LOAD:0000000000BEC0A8                 B               loc_BEC070
LOAD:0000000000BEC0AC ; ---------------------------------------------------------------------------
LOAD:0000000000BEC0AC
LOAD:0000000000BEC0AC loc_BEC0AC                              ; CODE XREF: BC_ISEQV+258↑j
LOAD:0000000000BEC0AC                 LDR             D1, [X28]
LOAD:0000000000BEC0B0                 SCVTF           D0, W0
LOAD:0000000000BEC0B4                 FCMP            D0, D1
LOAD:0000000000BEC0B8                 B               loc_BEC070
LOAD:0000000000BEC0BC ; ---------------------------------------------------------------------------
LOAD:0000000000BEC0BC
LOAD:0000000000BEC0BC loc_BEC0BC                              ; CODE XREF: BC_ISEQV:loc_BEC08C↑j
LOAD:0000000000BEC0BC                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEC0C0                 CMN             X15, #0xB
LOAD:0000000000BEC0C4                 B.NE            loc_BEC070
LOAD:0000000000BEC0C8                 B               loc_BEE32C
LOAD:0000000000BEC0C8 ; END OF FUNCTION CHUNK FOR BC_ISEQV
LOAD:0000000000BEC0CC
LOAD:0000000000BEC0CC ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEC0CC
LOAD:0000000000BEC0CC
LOAD:0000000000BEC0CC ; __int64 BC_ISEQP()
LOAD:0000000000BEC0CC BC_ISEQP
LOAD:0000000000BEC0CC                 LDR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEC0D0                 LDRH            W17, [X21,#2]
LOAD:0000000000BEC0D4                 ADD             X21, X21, #4
LOAD:0000000000BEC0D8                 ADD             X28, X28, #1
LOAD:0000000000BEC0DC                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEC0E0                 ASR             X15, X8, #0x2F ; '/'
LOAD:0000000000BEC0E4                 CMN             X15, #0xB
LOAD:0000000000BEC0E8                 B.EQ            loc_BEE32C
LOAD:0000000000BEC0EC                 CMN             X28, X15
LOAD:0000000000BEC0F0                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEC0F4                 CSEL            X21, X17, X21, EQ
LOAD:0000000000BEC0F8                 LDR             W16, [X21],#4
LOAD:0000000000BEC0FC                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC100                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC104                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC108                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC10C                 BR              X8
LOAD:0000000000BEC10C ; End of function BC_ISEQP
LOAD:0000000000BEC10C
LOAD:0000000000BEC110
LOAD:0000000000BEC110 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEC110
LOAD:0000000000BEC110
LOAD:0000000000BEC110 ; __int64 BC_ISNEP()
LOAD:0000000000BEC110 BC_ISNEP
LOAD:0000000000BEC110                 LDR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEC114                 LDRH            W17, [X21,#2]
LOAD:0000000000BEC118                 ADD             X21, X21, #4
LOAD:0000000000BEC11C                 ADD             X28, X28, #1
LOAD:0000000000BEC120                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEC124                 ASR             X15, X8, #0x2F ; '/'
LOAD:0000000000BEC128                 CMN             X15, #0xB
LOAD:0000000000BEC12C                 B.EQ            loc_BEE32C
LOAD:0000000000BEC130                 CMN             X28, X15
LOAD:0000000000BEC134                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEC138                 CSEL            X21, X17, X21, NE
LOAD:0000000000BEC13C                 LDR             W16, [X21],#4
LOAD:0000000000BEC140                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC144                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC148                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC14C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC150                 BR              X8