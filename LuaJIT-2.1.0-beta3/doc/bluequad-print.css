/* Copyright (C) 2004-2017 Mike <PERSON>.
 *
 * You are welcome to use the general ideas of this design for your own sites.
 * But please do not steal the stylesheet, the layout or the color scheme.
 */
body {
  font-family: serif;
  font-size: 11pt;
  margin: 0 3em;
  padding: 0;
  border: none;
}
a:link, a:visited, a:hover, a:active {
  text-decoration: none;
  background: transparent;
  color: #0000ff;
}
h1, h2, h3 {
  font-family: sans-serif;
  font-weight: bold;
  text-align: left;
  margin: 0.5em 0;
  padding: 0;
}
h1 {
  font-size: 200%;
}
h2 {
  font-size: 150%;
}
h3 {
  font-size: 125%;
}
p {
  margin: 0 0 0.5em 0;
  padding: 0;
}
ul, ol {
  margin: 0.5em 0;
  padding: 0 0 0 2em;
}
ul {
  list-style: outside square;
}
ol {
  list-style: outside decimal;
}
li {
  margin: 0;
  padding: 0;
}
dl {
  margin: 1em 0;
  padding: 1em;
  border: 1px solid black;
}
dt {
  font-weight: bold;
  margin: 0;
  padding: 0;
}
dt sup {
  float: right;
  margin-left: 1em;
}
dd {
  margin: 0.5em 0 0 2em;
  padding: 0;
}
table {
  table-layout: fixed;
  width: 100%;
  margin: 1em 0;
  padding: 0;
  border: 1px solid black;
  border-spacing: 0;
  border-collapse: collapse;
}
tr {
  margin: 0;
  padding: 0;
  border: none;
}
td {
  text-align: left;
  margin: 0;
  padding: 0.2em 0.5em;
  border-top: 1px solid black;
  border-bottom: 1px solid black;
}
tr.separate td {
  border-top: double;
}
tt, pre, code, kbd, samp {
  font-family: monospace;
  font-size: 75%;
}
kbd {
  font-weight: bolder;
}
blockquote, pre {
  margin: 1em 2em;
  padding: 0;
}
img {
  border: none;
  vertical-align: baseline;
  margin: 0;
  padding: 0;
}
img.left {
  float: left;
  margin: 0.5em 1em 0.5em 0;
}
img.right {
  float: right;
  margin: 0.5em 0 0.5em 1em;
}
.flush {
  clear: both;
  visibility: hidden;
}
.hide, .noprint, #nav {
  display: none !important;
}
.pagebreak {
  page-break-before: always;
}
#site {
  text-align: right;
  font-family: sans-serif;
  font-weight: bold;
  margin: 0 1em;
  border-bottom: 1pt solid black;
}
#site a {
  font-size: 1.2em;
}
#site a:link, #site a:visited {
  text-decoration: none;
  font-weight: bold;
  background: transparent;
  color: #ffffff;
}
#logo {
  color: #ff8000;
}
#head {
  clear: both;
  margin: 0 1em;
}
#main {
  line-height: 1.3;
  text-align: justify;
  margin: 1em;
}
#foot {
  clear: both;
  font-size: 80%;
  text-align: center;
  margin: 0 1.25em;
  padding: 0.5em 0 0 0;
  border-top: 1pt solid black;
  page-break-before: avoid;
  page-break-after: avoid;
}
