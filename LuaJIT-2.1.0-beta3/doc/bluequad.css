/* Copyright (C) 2004-2017 Mike <PERSON>.
 *
 * You are welcome to use the general ideas of this design for your own sites.
 * But please do not steal the stylesheet, the layout or the color scheme.
 */
/* colorscheme:
 *
 * site  |  head   #4162bf/white   | #6078bf/#e6ecff
 * ------+------   ----------------+-------------------
 * nav   |  main   #bfcfff         | #e6ecff/black
 *
 * nav:  hiback   loback     #c5d5ff #b9c9f9
 *       hiborder loborder   #e6ecff #97a7d7
 *       link     hover      #2142bf #ff0000
 *
 * link: link visited hover  #2142bf #8122bf #ff0000
 *
 * main: boxback  boxborder  #f0f4ff #bfcfff
 */
body {
  font-family: Verdana, Arial, Helvetica, sans-serif;
  font-size: 10pt;
  margin: 0;
  padding: 0;
  border: none;
  background: #e0e0e0;
  color: #000000;
}
a:link {
  text-decoration: none;
  background: transparent;
  color: #2142bf;
}
a:visited {
  text-decoration: none;
  background: transparent;
  color: #8122bf;
}
a:hover, a:active {
  text-decoration: underline;
  background: transparent;
  color: #ff0000;
}
h1, h2, h3 {
  font-weight: bold;
  text-align: left;
  margin: 0.5em 0;
  padding: 0;
  background: transparent;
}
h1 {
  font-size: 200%;
  line-height: 3em; /* really 6em relative to body, match #site span */
  margin: 0;
}
h2 {
  font-size: 150%;
  color: #606060;
}
h3 {
  font-size: 125%;
  color: #404040;
}
p {
  max-width: 600px;
  margin: 0 0 0.5em 0;
  padding: 0;
}
b {
  color: #404040;
}
ul, ol {
  max-width: 600px;
  margin: 0.5em 0;
  padding: 0 0 0 2em;
}
ul {
  list-style: outside square;
}
ol {
  list-style: outside decimal;
}
li {
  margin: 0;
  padding: 0;
}
dl {
  max-width: 600px;
  margin: 1em 0;
  padding: 1em;
  border: 1px solid #bfcfff;
  background: #f0f4ff;
}
dt {
  font-weight: bold;
  margin: 0;
  padding: 0;
}
dt sup {
  float: right;
  margin-left: 1em;
  color: #808080;
}
dt a:visited {
  text-decoration: none;
  color: #2142bf;
}
dt a:hover, dt a:active {
  text-decoration: none;
  color: #ff0000;
}
dd {
  margin: 0.5em 0 0 2em;
  padding: 0;
}
div.tablewrap { /* for IE *sigh* */
  max-width: 600px;
}
table {
  table-layout: fixed;
  border-spacing: 0;
  border-collapse: collapse;
  max-width: 600px;
  width: 100%;
  margin: 1em 0;
  padding: 0;
  border: 1px solid #bfcfff;
}
tr {
  margin: 0;
  padding: 0;
  border: none;
}
tr.odd {
  background: #f0f4ff;
}
tr.separate td {
  border-top: 1px solid #bfcfff;
}
td {
  text-align: left;
  margin: 0;
  padding: 0.2em 0.5em;
  border: none;
}
tt, code, kbd, samp {
  font-family: Courier New, Courier, monospace;
  line-height: 1.2;
  font-size: 110%;
}
kbd {
  font-weight: bolder;
}
blockquote, pre {
  max-width: 600px;
  margin: 1em 2em;
  padding: 0;
}
pre {
  line-height: 1.1;
}
pre.code {
  line-height: 1.4;
  margin: 0.5em 0 1em 0.5em;
  padding: 0.5em 1em;
  border: 1px solid #bfcfff;
  background: #f0f4ff;
}
pre.mark {
  padding-left: 2em;
}
span.codemark {
  position:absolute;
  left: 16em;
  color: #4040c0;
}
span.mark {
  color: #4040c0;
  font-family: Courier New, Courier, monospace;
  line-height: 1.1;
}
img {
  border: none;
  vertical-align: baseline;
  margin: 0;
  padding: 0;
}
img.left {
  float: left;
  margin: 0.5em 1em 0.5em 0;
}
img.right {
  float: right;
  margin: 0.5em 0 0.5em 1em;
}
.indent {
  padding-left: 1em;
}
.flush {
  clear: both;
  visibility: hidden;
}
.hide, .noscreen {
  display: none !important;
}
.ext {
  color: #ff8000;
}
.new {
  font-size: 6pt;
  vertical-align: middle;
  background: #ff8000;
  color: #ffffff;
}
#site {
  clear: both;
  float: left;
  width: 13em;
  text-align: center;
  font-weight: bold;
  margin: 0;
  padding: 0;
  background: transparent;
  color: #ffffff;
}
#site a {
  font-size: 200%;
}
#site a:link, #site a:visited {
  text-decoration: none;
  font-weight: bold;
  background: transparent;
  color: #ffffff;
}
#site span {
  line-height: 3em; /* really 6em relative to body, match h1 */
}
#logo {
  color: #ffb380;
}
#head {
  margin: 0;
  padding: 0 0 0 2em;
  border-left: solid 13em #4162bf;
  border-right: solid 3em #6078bf;
  background: #6078bf;
  color: #e6ecff;
}
#nav {
  clear: both;
  float: left;
  overflow: hidden;
  text-align: left;
  line-height: 1.5;
  width: 13em;
  padding-top: 1em;
  background: transparent;
}
#nav ul {
  list-style: none outside;
  margin: 0;
  padding: 0;
}
#nav li {
  margin: 0;
  padding: 0;
}
#nav a {
  display: block;
  text-decoration: none;
  font-weight: bold;
  margin: 0;
  padding: 2px 1em;
  border-top: 1px solid transparent;
  border-bottom: 1px solid transparent;
  background: transparent;
  color: #2142bf;
}
#nav a:hover, #nav a:active {
  text-decoration: none;
  border-top: 1px solid #97a7d7;
  border-bottom: 1px solid #e6ecff;
  background: #b9c9f9;
  color: #ff0000;
}
#nav a.current, #nav a.current:hover, #nav a.current:active {
  border-top: 1px solid #e6ecff;
  border-bottom: 1px solid #97a7d7;
  background: #c5d5ff;
  color: #2142bf;
}
#nav ul ul a {
  padding: 0 1em 0 1.7em;
}
#nav ul ul ul a {
  padding: 0 0.5em 0 2.4em;
}
#main {
  line-height: 1.5;
  text-align: left;
  margin: 0;
  padding: 1em 2em;
  border-left: solid 13em #bfcfff;
  border-right: solid 3em #e6ecff;
  background: #e6ecff;
}
#foot {
  clear: both;
  font-size: 80%;
  text-align: center;
  margin: 0;
  padding: 0.5em;
  background: #6078bf;
  color: #ffffff;
}
#foot a:link, #foot a:visited {
  text-decoration: underline;
  background: transparent;
  color: #ffffff;
}
#foot a:hover, #foot a:active {
  text-decoration: underline;
  background: transparent;
  color: #bfcfff;
}
