<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<title>Running LuaJIT</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta name="Author" content="<PERSON>">
<meta name="Copyright" content="Copyright (C) 2005-2017, <PERSON>">
<meta name="Language" content="en">
<link rel="stylesheet" type="text/css" href="bluequad.css" media="screen">
<link rel="stylesheet" type="text/css" href="bluequad-print.css" media="print">
<style type="text/css">
table.opt {
  line-height: 1.2;
}
tr.opthead td {
  font-weight: bold;
}
td.flag_name {
  width: 4em;
}
td.flag_level {
  width: 2em;
  text-align: center;
}
td.param_name {
  width: 6em;
}
td.param_default {
  width: 4em;
  text-align: right;
}
</style>
</head>
<body>
<div id="site">
<a href="http://luajit.org"><span>Lua<span id="logo">JIT</span></span></a>
</div>
<div id="head">
<h1>Running LuaJIT</h1>
</div>
<div id="nav">
<ul><li>
<a href="luajit.html">LuaJIT</a>
<ul><li>
<a href="http://luajit.org/download.html">Download <span class="ext">&raquo;</span></a>
</li><li>
<a href="install.html">Installation</a>
</li><li>
<a class="current" href="running.html">Running</a>
</li></ul>
</li><li>
<a href="extensions.html">Extensions</a>
<ul><li>
<a href="ext_ffi.html">FFI Library</a>
<ul><li>
<a href="ext_ffi_tutorial.html">FFI Tutorial</a>
</li><li>
<a href="ext_ffi_api.html">ffi.* API</a>
</li><li>
<a href="ext_ffi_semantics.html">FFI Semantics</a>
</li></ul>
</li><li>
<a href="ext_jit.html">jit.* Library</a>
</li><li>
<a href="ext_c_api.html">Lua/C API</a>
</li><li>
<a href="ext_profiler.html">Profiler</a>
</li></ul>
</li><li>
<a href="status.html">Status</a>
<ul><li>
<a href="changes.html">Changes</a>
</li></ul>
</li><li>
<a href="faq.html">FAQ</a>
</li><li>
<a href="http://luajit.org/performance.html">Performance <span class="ext">&raquo;</span></a>
</li><li>
<a href="http://wiki.luajit.org/">Wiki <span class="ext">&raquo;</span></a>
</li><li>
<a href="http://luajit.org/list.html">Mailing List <span class="ext">&raquo;</span></a>
</li></ul>
</div>
<div id="main">
<p>
LuaJIT has only a single stand-alone executable, called <tt>luajit</tt> on
POSIX systems or <tt>luajit.exe</tt> on Windows. It can be used to run simple
Lua statements or whole Lua applications from the command line. It has an
interactive mode, too.
</p>

<h2 id="options">Command Line Options</h2>
<p>
The <tt>luajit</tt> stand-alone executable is just a slightly modified
version of the regular <tt>lua</tt> stand-alone executable.
It supports the same basic options, too. <tt>luajit&nbsp;-h</tt>
prints a short list of the available options. Please have a look at the
<a href="http://www.lua.org/manual/5.1/manual.html#6"><span class="ext">&raquo;</span>&nbsp;Lua manual</a>
for details.
</p>
<p>
LuaJIT has some additional options:
</p>

<h3 id="opt_b"><tt>-b[options] input output</tt></h3>
<p>
This option saves or lists bytecode. The following additional options
are accepted:
</p>
<ul>
<li><tt>-l</tt> &mdash; Only list bytecode.</li>
<li><tt>-s</tt> &mdash; Strip debug info (this is the default).</li>
<li><tt>-g</tt> &mdash; Keep debug info.</li>
<li><tt>-n name</tt> &mdash; Set module name (default: auto-detect from input name)</li>
<li><tt>-t type</tt> &mdash; Set output file type (default: auto-detect from output name).</li>
<li><tt>-a arch</tt> &mdash; Override architecture for object files (default: native).</li>
<li><tt>-o os</tt> &mdash; Override OS for object files (default: native).</li>
<li><tt>-e chunk</tt> &mdash; Use chunk string as input.</li>
<li><tt>-</tt> (a single minus sign) &mdash; Use stdin as input and/or stdout as output.</li>
</ul>
<p>
The output file type is auto-detected from the extension of the output
file name:
</p>
<ul>
<li><tt>c</tt> &mdash; C source file, exported bytecode data.</li>
<li><tt>h</tt> &mdash; C header file, static bytecode data.</li>
<li><tt>obj</tt> or <tt>o</tt> &mdash; Object file, exported bytecode data
(OS- and architecture-specific).</li>
<li><tt>raw</tt> or any other extension &mdash; Raw bytecode file (portable).
</ul>
<p>
Notes:
</p>
<ul>
<li>See also <a href="extensions.html#string_dump">string.dump()</a>
for information on bytecode portability and compatibility.</li>
<li>A file in raw bytecode format is auto-detected and can be loaded like
any Lua source file. E.g. directly from the command line or with
<tt>loadfile()</tt>, <tt>dofile()</tt> etc.</li>
<li>To statically embed the bytecode of a module in your application,
generate an object file and just link it with your application.</li>
<li>On most ELF-based systems (e.g. Linux) you need to explicitly export the
global symbols when linking your application, e.g. with: <tt>-Wl,-E</tt></li>
<li><tt>require()</tt> tries to load embedded bytecode data from exported
symbols (in <tt>*.exe</tt> or <tt>lua51.dll</tt> on Windows) and from
shared libraries in <tt>package.cpath</tt>.</li>
</ul>
<p>
Typical usage examples:
</p>
<pre class="code">
luajit -b test.lua test.out                 # Save bytecode to test.out
luajit -bg test.lua test.out                # Keep debug info
luajit -be "print('hello world')" test.out  # Save cmdline script

luajit -bl test.lua                         # List to stdout
luajit -bl test.lua test.txt                # List to test.txt
luajit -ble "print('hello world')"          # List cmdline script

luajit -b test.lua test.obj                 # Generate object file
# Link test.obj with your application and load it with require("test")
</pre>

<h3 id="opt_j"><tt>-j cmd[=arg[,arg...]]</tt></h3>
<p>
This option performs a LuaJIT control command or activates one of the
loadable extension modules. The command is first looked up in the
<tt>jit.*</tt> library. If no matching function is found, a module
named <tt>jit.&lt;cmd&gt;</tt> is loaded and the <tt>start()</tt>
function of the module is called with the specified arguments (if
any). The space between <tt>-j</tt> and <tt>cmd</tt> is optional.
</p>
<p>
Here are the available LuaJIT control commands:
</p>
<ul>
<li id="j_on"><tt>-jon</tt> &mdash; Turns the JIT compiler on (default).</li>
<li id="j_off"><tt>-joff</tt> &mdash; Turns the JIT compiler off (only use the interpreter).</li>
<li id="j_flush"><tt>-jflush</tt> &mdash; Flushes the whole cache of compiled code.</li>
<li id="j_v"><tt>-jv</tt> &mdash; Shows verbose information about the progress of the JIT compiler.</li>
<li id="j_dump"><tt>-jdump</tt> &mdash; Dumps the code and structures used in various compiler stages.</li>
<li id="j_p"><tt>-jp</tt> &mdash; Start the <a href="ext_profiler.html">integrated profiler</a>.</li>
</ul>
<p>
The <tt>-jv</tt> and <tt>-jdump</tt> commands are extension modules
written in Lua. They are mainly used for debugging the JIT compiler
itself. For a description of their options and output format, please
read the comment block at the start of their source.
They can be found in the <tt>lib</tt> directory of the source
distribution or installed under the <tt>jit</tt> directory. By default
this is <tt>/usr/local/share/luajit-2.0.5/jit</tt> on POSIX
systems.
</p>

<h3 id="opt_O"><tt>-O[level]</tt><br>
<tt>-O[+]flag</tt>&nbsp;&nbsp;&nbsp;<tt>-O-flag</tt><br>
<tt>-Oparam=value</tt></h3>
<p>
This options allows fine-tuned control of the optimizations used by
the JIT compiler. This is mainly intended for debugging LuaJIT itself.
Please note that the JIT compiler is extremely fast (we are talking
about the microsecond to millisecond range). Disabling optimizations
doesn't have any visible impact on its overhead, but usually generates
code that runs slower.
</p>
<p>
The first form sets an optimization level &mdash; this enables a
specific mix of optimization flags. <tt>-O0</tt> turns off all
optimizations and higher numbers enable more optimizations. Omitting
the level (i.e. just <tt>-O</tt>) sets the default optimization level,
which is <tt>-O3</tt> in the current version.
</p>
<p>
The second form adds or removes individual optimization flags.
The third form sets a parameter for the VM or the JIT compiler
to a specific value.
</p>
<p>
You can either use this option multiple times (like <tt>-Ocse
-O-dce -Ohotloop=10</tt>) or separate several settings with a comma
(like <tt>-O+cse,-dce,hotloop=10</tt>). The settings are applied from
left to right and later settings override earlier ones. You can freely
mix the three forms, but note that setting an optimization level
overrides all earlier flags.
</p>
<p>
Here are the available flags and at what optimization levels they
are enabled:
</p>
<table class="opt">
<tr class="opthead">
<td class="flag_name">Flag</td>
<td class="flag_level">-O1</td>
<td class="flag_level">-O2</td>
<td class="flag_level">-O3</td>
<td class="flag_desc">&nbsp;</td>
</tr>
<tr class="odd separate">
<td class="flag_name">fold</td><td class="flag_level">&bull;</td><td class="flag_level">&bull;</td><td class="flag_level">&bull;</td><td class="flag_desc">Constant Folding, Simplifications and Reassociation</td></tr>
<tr class="even">
<td class="flag_name">cse</td><td class="flag_level">&bull;</td><td class="flag_level">&bull;</td><td class="flag_level">&bull;</td><td class="flag_desc">Common-Subexpression Elimination</td></tr>
<tr class="odd">
<td class="flag_name">dce</td><td class="flag_level">&bull;</td><td class="flag_level">&bull;</td><td class="flag_level">&bull;</td><td class="flag_desc">Dead-Code Elimination</td></tr>
<tr class="even">
<td class="flag_name">narrow</td><td class="flag_level">&nbsp;</td><td class="flag_level">&bull;</td><td class="flag_level">&bull;</td><td class="flag_desc">Narrowing of numbers to integers</td></tr>
<tr class="odd">
<td class="flag_name">loop</td><td class="flag_level">&nbsp;</td><td class="flag_level">&bull;</td><td class="flag_level">&bull;</td><td class="flag_desc">Loop Optimizations (code hoisting)</td></tr>
<tr class="even">
<td class="flag_name">fwd</td><td class="flag_level">&nbsp;</td><td class="flag_level">&nbsp;</td><td class="flag_level">&bull;</td><td class="flag_desc">Load Forwarding (L2L) and Store Forwarding (S2L)</td></tr>
<tr class="odd">
<td class="flag_name">dse</td><td class="flag_level">&nbsp;</td><td class="flag_level">&nbsp;</td><td class="flag_level">&bull;</td><td class="flag_desc">Dead-Store Elimination</td></tr>
<tr class="even">
<td class="flag_name">abc</td><td class="flag_level">&nbsp;</td><td class="flag_level">&nbsp;</td><td class="flag_level">&bull;</td><td class="flag_desc">Array Bounds Check Elimination</td></tr>
<tr class="odd">
<td class="flag_name">sink</td><td class="flag_level">&nbsp;</td><td class="flag_level">&nbsp;</td><td class="flag_level">&bull;</td><td class="flag_desc">Allocation/Store Sinking</td></tr>
<tr class="even">
<td class="flag_name">fuse</td><td class="flag_level">&nbsp;</td><td class="flag_level">&nbsp;</td><td class="flag_level">&bull;</td><td class="flag_desc">Fusion of operands into instructions</td></tr>
</table>
<p>
Here are the parameters and their default settings:
</p>
<table class="opt">
<tr class="opthead">
<td class="param_name">Parameter</td>
<td class="param_default">Default</td>
<td class="param_desc">&nbsp;</td>
</tr>
<tr class="odd separate">
<td class="param_name">maxtrace</td><td class="param_default">1000</td><td class="param_desc">Max. number of traces in the cache</td></tr>
<tr class="even">
<td class="param_name">maxrecord</td><td class="param_default">4000</td><td class="param_desc">Max. number of recorded IR instructions</td></tr>
<tr class="odd">
<td class="param_name">maxirconst</td><td class="param_default">500</td><td class="param_desc">Max. number of IR constants of a trace</td></tr>
<tr class="even">
<td class="param_name">maxside</td><td class="param_default">100</td><td class="param_desc">Max. number of side traces of a root trace</td></tr>
<tr class="odd">
<td class="param_name">maxsnap</td><td class="param_default">500</td><td class="param_desc">Max. number of snapshots for a trace</td></tr>
<tr class="even separate">
<td class="param_name">hotloop</td><td class="param_default">56</td><td class="param_desc">Number of iterations to detect a hot loop or hot call</td></tr>
<tr class="odd">
<td class="param_name">hotexit</td><td class="param_default">10</td><td class="param_desc">Number of taken exits to start a side trace</td></tr>
<tr class="even">
<td class="param_name">tryside</td><td class="param_default">4</td><td class="param_desc">Number of attempts to compile a side trace</td></tr>
<tr class="odd separate">
<td class="param_name">instunroll</td><td class="param_default">4</td><td class="param_desc">Max. unroll factor for instable loops</td></tr>
<tr class="even">
<td class="param_name">loopunroll</td><td class="param_default">15</td><td class="param_desc">Max. unroll factor for loop ops in side traces</td></tr>
<tr class="odd">
<td class="param_name">callunroll</td><td class="param_default">3</td><td class="param_desc">Max. unroll factor for pseudo-recursive calls</td></tr>
<tr class="even">
<td class="param_name">recunroll</td><td class="param_default">2</td><td class="param_desc">Min. unroll factor for true recursion</td></tr>
<tr class="odd separate">
<td class="param_name">sizemcode</td><td class="param_default">32</td><td class="param_desc">Size of each machine code area in KBytes (Windows: 64K)</td></tr>
<tr class="even">
<td class="param_name">maxmcode</td><td class="param_default">512</td><td class="param_desc">Max. total size of all machine code areas in KBytes</td></tr>
</table>
<br class="flush">
</div>
<div id="foot">
<hr class="hide">
Copyright &copy; 2005-2017 Mike Pall
<span class="noprint">
&middot;
<a href="contact.html">Contact</a>
</span>
</div>
</body>
</html>
