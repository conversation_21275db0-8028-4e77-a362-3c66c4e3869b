<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
<title>Status</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta name="Author" content="<PERSON>">
<meta name="Copyright" content="Copyright (C) 2005-2017, <PERSON>">
<meta name="Language" content="en">
<link rel="stylesheet" type="text/css" href="bluequad.css" media="screen">
<link rel="stylesheet" type="text/css" href="bluequad-print.css" media="print">
<style type="text/css">
ul li { padding-bottom: 0.3em; }
</style>
</head>
<body>
<div id="site">
<a href="http://luajit.org"><span>Lua<span id="logo">JIT</span></span></a>
</div>
<div id="head">
<h1>Status</h1>
</div>
<div id="nav">
<ul><li>
<a href="luajit.html">LuaJIT</a>
<ul><li>
<a href="http://luajit.org/download.html">Download <span class="ext">&raquo;</span></a>
</li><li>
<a href="install.html">Installation</a>
</li><li>
<a href="running.html">Running</a>
</li></ul>
</li><li>
<a href="extensions.html">Extensions</a>
<ul><li>
<a href="ext_ffi.html">FFI Library</a>
<ul><li>
<a href="ext_ffi_tutorial.html">FFI Tutorial</a>
</li><li>
<a href="ext_ffi_api.html">ffi.* API</a>
</li><li>
<a href="ext_ffi_semantics.html">FFI Semantics</a>
</li></ul>
</li><li>
<a href="ext_jit.html">jit.* Library</a>
</li><li>
<a href="ext_c_api.html">Lua/C API</a>
</li><li>
<a href="ext_profiler.html">Profiler</a>
</li></ul>
</li><li>
<a class="current" href="status.html">Status</a>
<ul><li>
<a href="changes.html">Changes</a>
</li></ul>
</li><li>
<a href="faq.html">FAQ</a>
</li><li>
<a href="http://luajit.org/performance.html">Performance <span class="ext">&raquo;</span></a>
</li><li>
<a href="http://wiki.luajit.org/">Wiki <span class="ext">&raquo;</span></a>
</li><li>
<a href="http://luajit.org/list.html">Mailing List <span class="ext">&raquo;</span></a>
</li></ul>
</div>
<div id="main">
<p>
<span style="color: #0000c0;">LuaJIT&nbsp;2.0</span> is the current
<span style="color: #0000c0;">stable branch</span>. This branch is in
feature-freeze &mdash; new features will only be added to LuaJIT&nbsp;2.1.
</p>

<h2>Current Status</h2>
<p>
LuaJIT ought to run all Lua&nbsp;5.1-compatible source code just fine.
It's considered a serious bug if the VM crashes or produces unexpected
results &mdash; please report this.
</p>
<p>
Known incompatibilities and issues in LuaJIT&nbsp;2.0:
</p>
<ul>
<li>
There are some differences in <b>implementation-defined</b> behavior.
These either have a good reason, are arbitrary design choices
or are due to quirks in the VM. The latter cases may get fixed if a
demonstrable need is shown.
</li>
<li>
The Lua <b>debug API</b> is missing a couple of features (return
hooks for non-Lua functions) and shows slightly different behavior
in LuaJIT (no per-coroutine hooks, no tail call counting).
</li>
<li>
Currently some <b>out-of-memory</b> errors from <b>on-trace code</b> are not
handled correctly. The error may fall through an on-trace
<tt>pcall</tt> or it may be passed on to the function set with
<tt>lua_atpanic</tt> on x64. This issue will be fixed with the new
garbage collector.
</li>
<li>
LuaJIT on 64 bit systems provides a <b>limited range</b> of 47 bits for the
<b>legacy <tt>lightuserdata</tt></b> data type.
This is only relevant on x64 systems which use the negative part of the
virtual address space in user mode, e.g. Solaris/x64, and on ARM64 systems
configured with a 48 bit or 52 bit VA.
Avoid using <tt>lightuserdata</tt> to hold pointers that may point outside
of that range, e.g. variables on the stack. In general, avoid this data
type for new code and replace it with (much more performant) FFI bindings.
FFI cdata pointers can address the full 64 bit range.
</li>
</ul>
<br class="flush">
</div>
<div id="foot">
<hr class="hide">
Copyright &copy; 2005-2017 Mike Pall
<span class="noprint">
&middot;
<a href="contact.html">Contact</a>
</span>
</div>
</body>
</html>
