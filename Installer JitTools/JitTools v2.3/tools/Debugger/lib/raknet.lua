-- This file is part of the SAMP.Lua project.
-- Licensed under the MIT License.
-- Copyright (c) 2016, FYP @ BlastHack Team <blast.hk>
-- https://github.com/THE-FYP/SAMP.Lua


local MODULE =
{
	MODULEINFO = {
		name = 'samp.raknet',
		version = 1
	}
}
require 'lib.sampfuncs'

MODULE.RPC = {
	CLICKPLAYER                   = RPC_CLICKPLAYER,
	CLIENTJOIN                    = RPC_CLIENTJOIN,
	ENTERVEHICLE                  = RPC_ENTERVEHICLE,
	ENTEREDITOBJECT               = RPC_ENTEREDITOBJECT,
	SCRIPTCASH                    = RPC_SCRIPTCASH,
	SERVERCOMMAND                 = RPC_SERVERCOMMAND,
	SPAWN                         = RPC_SPAWN,
	DEATH                         = RPC_DEATH,
	NPCJOIN                       = RPC_NPCJOIN,
	DIALOGRESPONSE                = RPC_DIALOGRESPONSE,
	CLICKTEXTDRAW                 = RPC_CLICKTEXTDRAW,
	SCMEVENT                      = RPC_SCMEVENT,
	WEAPONPICKUPDESTROY           = RPC_WEAPONPICKUPDESTROY,
	CHAT                          = RPC_CHAT,
	SRVNETSTATS                   = RPC_SRVNETSTATS,
	CLIENTCHECK                   = RPC_CLIENTCHECK,
	DAMAGEVEHICLE                 = RPC_DAMAGEVEHICLE,
	GIVETAKEDAMAGE                = RPC_GIVETAKEDAMAGE,
	EDITATTACHEDOBJECT            = RPC_EDITATTACHEDOBJECT,
	EDITOBJECT                    = RPC_EDITOBJECT,
	SETINTERIORID                 = RPC_SETINTERIORID,
	MAPMARKER                     = RPC_MAPMARKER,
	REQUESTCLASS                  = RPC_REQUESTCLASS,
	REQUESTSPAWN                  = RPC_REQUESTSPAWN,
	PICKEDUPPICKUP                = RPC_PICKEDUPPICKUP,
	MENUSELECT                    = RPC_MENUSELECT,
	VEHICLEDESTROYED              = RPC_VEHICLEDESTROYED,
	MENUQUIT                      = RPC_MENUQUIT,
	EXITVEHICLE                   = RPC_EXITVEHICLE,
	UPDATESCORESPINGSIPS          = RPC_UPDATESCORESPINGSIPS,

	CONNECTIONREJECTED            = 130,
	SETPLAYERNAME                 = RPC_SCRSETPLAYERNAME,
	SETPLAYERPOS                  = RPC_SCRSETPLAYERPOS,
	SETPLAYERPOSFINDZ             = RPC_SCRSETPLAYERPOSFINDZ,
	SETPLAYERHEALTH               = RPC_SCRSETPLAYERHEALTH,
	TOGGLEPLAYERCONTROLLABLE      = RPC_SCRTOGGLEPLAYERCONTROLLABLE,
	PLAYSOUND                     = RPC_SCRPLAYSOUND,
	SETPLAYERWORLDBOUNDS          = RPC_SCRSETPLAYERWORLDBOUNDS,
	GIVEPLAYERMONEY               = RPC_SCRGIVEPLAYERMONEY,
	SETPLAYERFACINGANGLE          = RPC_SCRSETPLAYERFACINGANGLE,
	RESETPLAYERMONEY              = RPC_SCRRESETPLAYERMONEY,
	RESETPLAYERWEAPONS            = RPC_SCRRESETPLAYERWEAPONS,
	GIVEPLAYERWEAPON              = RPC_SCRGIVEPLAYERWEAPON,
	SETVEHICLEPARAMSEX            = RPC_SCRSETVEHICLEPARAMSEX,
	CANCELEDIT                    = RPC_SCRCANCELEDIT,
	SETPLAYERTIME                 = RPC_SCRSETPLAYERTIME,
	TOGGLECLOCK                   = RPC_SCRTOGGLECLOCK,
	WORLDPLAYERADD                = RPC_SCRWORLDPLAYERADD,
	SETPLAYERSHOPNAME             = RPC_SCRSETPLAYERSHOPNAME,
	SETPLAYERSKILLLEVEL           = RPC_SCRSETPLAYERSKILLLEVEL,
	SETPLAYERDRUNKLEVEL           = RPC_SCRSETPLAYERDRUNKLEVEL,
	CREATE3DTEXTLABEL             = RPC_SCRCREATE3DTEXTLABEL,
	DISABLECHECKPOINT             = RPC_SCRDISABLECHECKPOINT,
	SETRACECHECKPOINT             = RPC_SCRSETRACECHECKPOINT,
	DISABLERACECHECKPOINT         = RPC_SCRDISABLERACECHECKPOINT,
	GAMEMODERESTART               = RPC_SCRGAMEMODERESTART,
	PLAYAUDIOSTREAM               = RPC_SCRPLAYAUDIOSTREAM,
	STOPAUDIOSTREAM               = RPC_SCRSTOPAUDIOSTREAM,
	REMOVEBUILDINGFORPLAYER       = RPC_SCRREMOVEBUILDINGFORPLAYER,
	CREATEOBJECT                  = RPC_SCRCREATEOBJECT,
	SETOBJECTPOS                  = RPC_SCRSETOBJECTPOS,
	SETOBJECTROT                  = RPC_SCRSETOBJECTROT,
	DESTROYOBJECT                 = RPC_SCRDESTROYOBJECT,
	DEATHMESSAGE                  = RPC_SCRDEATHMESSAGE,
	SETPLAYERMAPICON              = RPC_SCRSETPLAYERMAPICON,
	REMOVEVEHICLECOMPONENT        = RPC_SCRREMOVEVEHICLECOMPONENT,
	UPDATE3DTEXTLABEL             = RPC_SCRUPDATE3DTEXTLABEL,
	CHATBUBBLE                    = RPC_SCRCHATBUBBLE,
	UPDATETIME                    = RPC_SCRSOMEUPDATE,
	SHOWDIALOG                    = RPC_SCRSHOWDIALOG,
	DESTROYPICKUP                 = RPC_SCRDESTROYPICKUP,
	LINKVEHICLETOINTERIOR         = RPC_SCRLINKVEHICLETOINTERIOR,
	SETPLAYERARMOUR               = RPC_SCRSETPLAYERARMOUR,
	SETPLAYERARMEDWEAPON          = RPC_SCRSETPLAYERARMEDWEAPON,
	SETSPAWNINFO                  = RPC_SCRSETSPAWNINFO,
	SETPLAYERTEAM                 = RPC_SCRSETPLAYERTEAM,
	PUTPLAYERINVEHICLE            = RPC_SCRPUTPLAYERINVEHICLE,
	REMOVEPLAYERFROMVEHICLE       = RPC_SCRREMOVEPLAYERFROMVEHICLE,
	SETPLAYERCOLOR                = RPC_SCRSETPLAYERCOLOR,
	DISPLAYGAMETEXT               = RPC_SCRDISPLAYGAMETEXT,
	FORCECLASSSELECTION           = RPC_SCRFORCECLASSSELECTION,
	ATTACHOBJECTTOPLAYER          = RPC_SCRATTACHOBJECTTOPLAYER,
	INITMENU                      = RPC_SCRINITMENU,
	SHOWMENU                      = RPC_SCRSHOWMENU,
	HIDEMENU                      = RPC_SCRHIDEMENU,
	CREATEEXPLOSION               = RPC_SCRCREATEEXPLOSION,
	SHOWPLAYERNAMETAGFORPLAYER    = RPC_SCRSHOWPLAYERNAMETAGFORPLAYER,
	ATTACHCAMERATOOBJECT          = RPC_SCRATTACHCAMERATOOBJECT,
	INTERPOLATECAMERA             = RPC_SCRINTERPOLATECAMERA,
	SETOBJECTMATERIAL             = RPC_SCRSETOBJECTMATERIAL,
	GANGZONESTOPFLASH             = RPC_SCRGANGZONESTOPFLASH,
	APPLYANIMATION                = RPC_SCRAPPLYANIMATION,
	CLEARANIMATIONS               = RPC_SCRCLEARANIMATIONS,
	SETPLAYERSPECIALACTION        = RPC_SCRSETPLAYERSPECIALACTION,
	SETPLAYERFIGHTINGSTYLE        = RPC_SCRSETPLAYERFIGHTINGSTYLE,
	SETPLAYERVELOCITY             = RPC_SCRSETPLAYERVELOCITY,
	SETVEHICLEVELOCITY            = RPC_SCRSETVEHICLEVELOCITY,
	CLIENTMESSAGE                 = RPC_SCRCLIENTMESSAGE,
	SETWORLDTIME                  = RPC_SCRSETWORLDTIME,
	CREATEPICKUP                  = RPC_SCRCREATEPICKUP,
	MOVEOBJECT                    = RPC_SCRMOVEOBJECT,
	ENABLESTUNTBONUSFORPLAYER     = RPC_SCRENABLESTUNTBONUSFORPLAYER,
	TEXTDRAWSETSTRING             = RPC_SCRTEXTDRAWSETSTRING,
	SETCHECKPOINT                 = RPC_SCRSETCHECKPOINT,
	GANGZONECREATE                = RPC_SCRGANGZONECREATE,
	PLAYCRIMEREPORT               = RPC_SCRPLAYCRIMEREPORT,
	SETPLAYERATTACHEDOBJECT       = RPC_SCRSETPLAYERATTACHEDOBJECT,
	GANGZONEDESTROY               = RPC_SCRGANGZONEDESTROY,
	GANGZONEFLASH                 = RPC_SCRGANGZONEFLASH,
	STOPOBJECT                    = RPC_SCRSTOPOBJECT,
	SETNUMBERPLATE                = RPC_SCRSETNUMBERPLATE,
	TOGGLEPLAYERSPECTATING        = RPC_SCRTOGGLEPLAYERSPECTATING,
	PLAYERSPECTATEPLAYER          = RPC_SCRPLAYERSPECTATEPLAYER,
	PLAYERSPECTATEVEHICLE         = RPC_SCRPLAYERSPECTATEVEHICLE,
	SETPLAYERWANTEDLEVEL          = RPC_SCRSETPLAYERWANTEDLEVEL,
	SHOWTEXTDRAW                  = RPC_SCRSHOWTEXTDRAW,
	TEXTDRAWHIDEFORPLAYER         = RPC_SCRTEXTDRAWHIDEFORPLAYER,
	SERVERJOIN                    = RPC_SCRSERVERJOIN,
	SERVERQUIT                    = RPC_SCRSERVERQUIT,
	INITGAME                      = RPC_SCRINITGAME,
	REMOVEPLAYERMAPICON           = RPC_SCRREMOVEPLAYERMAPICON,
	SETPLAYERAMMO                 = RPC_SCRSETPLAYERAMMO,
	SETGRAVITY                    = RPC_SCRSETGRAVITY,
	SETVEHICLEHEALTH              = RPC_SCRSETVEHICLEHEALTH,
	ATTACHTRAILERTOVEHICLE        = RPC_SCRATTACHTRAILERTOVEHICLE,
	DETACHTRAILERFROMVEHICLE      = RPC_SCRDETACHTRAILERFROMVEHICLE,
	SETWEATHER                    = RPC_SCRSETWEATHER,
	SETPLAYERSKIN                 = RPC_SCRSETPLAYERSKIN,
	SETPLAYERINTERIOR             = RPC_SCRSETPLAYERINTERIOR,
	SETPLAYERCAMERAPOS            = RPC_SCRSETPLAYERCAMERAPOS,
	SETPLAYERCAMERALOOKAT         = RPC_SCRSETPLAYERCAMERALOOKAT,
	SETVEHICLEPOS                 = RPC_SCRSETVEHICLEPOS,
	SETVEHICLEZANGLE              = RPC_SCRSETVEHICLEZANGLE,
	SETVEHICLEPARAMSFORPLAYER     = RPC_SCRSETVEHICLEPARAMSFORPLAYER,
	SETCAMERABEHINDPLAYER         = RPC_SCRSETCAMERABEHINDPLAYER,
	WORLDPLAYERREMOVE             = RPC_SCRWORLDPLAYERREMOVE,
	WORLDVEHICLEADD               = RPC_SCRWORLDVEHICLEADD,
	WORLDVEHICLEREMOVE            = RPC_SCRWORLDVEHICLEREMOVE,
	WORLDPLAYERDEATH              = RPC_SCRWORLDPLAYERDEATH
}

MODULE.PACKET = {
	VEHICLE_SYNC                      = PACKET_VEHICLE_SYNC,
	RCON_COMMAND                      = PACKET_RCON_COMMAND,
	RCON_RESPONCE                     = PACKET_RCON_RESPONCE,
	AIM_SYNC                          = PACKET_AIM_SYNC,
	WEAPONS_UPDATE                    = PACKET_WEAPONS_UPDATE,
	STATS_UPDATE                      = PACKET_STATS_UPDATE,
	BULLET_SYNC                       = PACKET_BULLET_SYNC,
	PLAYER_SYNC                       = PACKET_PLAYER_SYNC,
	MARKERS_SYNC                      = PACKET_MARKERS_SYNC,
	UNOCCUPIED_SYNC                   = PACKET_UNOCCUPIED_SYNC,
	TRAILER_SYNC                      = PACKET_TRAILER_SYNC,
	PASSENGER_SYNC                    = PACKET_PASSENGER_SYNC,
	SPECTATOR_SYNC                    = PACKET_SPECTATOR_SYNC,

	INTERNAL_PING                     = PACKET_INTERNAL_PING,
	PING                              = PACKET_PING,
	PING_OPEN_CONNECTIONS             = PACKET_PING_OPEN_CONNECTIONS,
	CONNECTED_PONG                    = PACKET_CONNECTED_PONG,
	REQUEST_STATIC_DATA               = PACKET_REQUEST_STATIC_DATA,
	CONNECTION_REQUEST                = PACKET_CONNECTION_REQUEST,
	AUTH_KEY                          = PACKET_AUTH_KEY,
	BROADCAST_PINGS                   = PACKET_BROADCAST_PINGS,
	SECURED_CONNECTION_RESPONSE       = PACKET_SECURED_CONNECTION_RESPONSE,
	SECURED_CONNECTION_CONFIRMATION   = PACKET_SECURED_CONNECTION_CONFIRMATION,
	RPC_MAPPING                       = PACKET_RPC_MAPPING,
	SET_RANDOM_NUMBER_SEED            = PACKET_SET_RANDOM_NUMBER_SEED,
	RPC                               = PACKET_RPC,
	RPC_REPLY                         = PACKET_RPC_REPLY,
	DETECT_LOST_CONNECTIONS           = PACKET_DETECT_LOST_CONNECTIONS,
	OPEN_CONNECTION_REQUEST           = PACKET_OPEN_CONNECTION_REQUEST,
	OPEN_CONNECTION_REPLY             = PACKET_OPEN_CONNECTION_REPLY,
	CONNECTION_COOKIE                 = PACKET_CONNECTION_COOKIE,
	RSA_PUBLIC_KEY_MISMATCH           = PACKET_RSA_PUBLIC_KEY_MISMATCH,
	CONNECTION_ATTEMPT_FAILED         = PACKET_CONNECTION_ATTEMPT_FAILED,
	NEW_INCOMING_CONNECTION           = PACKET_NEW_INCOMING_CONNECTION,
	NO_FREE_INCOMING_CONNECTIONS      = PACKET_NO_FREE_INCOMING_CONNECTIONS,
	DISCONNECTION_NOTIFICATION        = PACKET_DISCONNECTION_NOTIFICATION,
	CONNECTION_LOST                   = PACKET_CONNECTION_LOST,
	CONNECTION_REQUEST_ACCEPTED       = PACKET_CONNECTION_REQUEST_ACCEPTED,
	INITIALIZE_ENCRYPTION             = PACKET_INITIALIZE_ENCRYPTION,
	CONNECTION_BANNED                 = PACKET_CONNECTION_BANNED,
	INVALID_PASSWORD                  = PACKET_INVALID_PASSWORD,
	MODIFIED_PACKET                   = PACKET_MODIFIED_PACKET,
	PONG                              = PACKET_PONG,
	TIMESTAMP                         = PACKET_TIMESTAMP,
	RECEIVED_STATIC_DATA              = PACKET_RECEIVED_STATIC_DATA,
	REMOTE_DISCONNECTION_NOTIFICATION = PACKET_REMOTE_DISCONNECTION_NOTIFICATION,
	REMOTE_CONNECTION_LOST            = PACKET_REMOTE_CONNECTION_LOST,
	REMOTE_NEW_INCOMING_CONNECTION    = PACKET_REMOTE_NEW_INCOMING_CONNECTION,
	REMOTE_EXISTING_CONNECTION        = PACKET_REMOTE_EXISTING_CONNECTION,
	REMOTE_STATIC_DATA                = PACKET_REMOTE_STATIC_DATA,
	ADVERTISE_SYSTEM                  = PACKET_ADVERTISE_SYSTEM
}

return MODULE
