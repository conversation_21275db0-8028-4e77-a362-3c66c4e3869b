-- This file is part of SA MoonLoader package.
-- Licensed under the MIT License.
-- Copyright (c) 2016, BlastHack Team <blast.hk>


return {
---
-- default.ide
---
AIRTRAIN_VLO = 320,
GUN_DILDO1 = 321,
GUN_DILDO2 = 322,
GUN_VIBE1 = 323,
GUN_VIBE2 = 324,
FLOWERA = 325,
GUN_CANE = 326,
GUN_BOXWEE = 327,
GUN_BOXBIG = 328,
CELLPHONE = 330,
BRASSKNUCKLE = 331,
GOLFCLUB = 333,
NITESTICK = 334,
KNIFECUR = 335,
BAT = 336,
SHOVEL = 337,
POOLCUE = 338,
KATANA = 339,
CHNSAW = 341,
GRENADE = 342,
TEARGAS = 343,
MOLOTOV = 344,
MISSILE = 345,
COLT45 = 346,
SILENCED = 347,
DESERT_EAGLE = 348,
CHROMEGUN = 349,
SAWNOFF = 350,
SHOTGSPA = 351,
MICRO_UZI = 352,
MP5LNG = 353,
FLARE = 354,
AK47 = 355,
M4 = 356,
COUNTRYRIFLE = 357,
SNIPER = 358,
ROCKETLA = 359,
HEATSEEK = 360,
FLAME = 361,
MINIGUN = 362,
SATCHEL = 363,
BOMB = 364,
SPRAYCAN = 365,
FIRE_EX = 366,
CAMERA = 367,
NVGOGGLES = 368,
IRGOGGLES = 369,
JETPACK = 370,
GUN_PARA = 371,
TEC9 = 372,
ARMOUR = 373,
CSPLAY = 1,
CLOTHES01 = 384,
CLOTHES01 = 385,
CLOTHES01 = 386,
CLOTHES01 = 387,
CLOTHES01 = 388,
CLOTHES01 = 389,
CLOTHES01 = 390,
CLOTHES01 = 391,
CLOTHES01 = 392,
CLOTHES01 = 393,
SHANDL = 394,
SHANDR = 395,
FHANDL = 396,
FHANDR = 397,
CUTOBJ01 = 300,
CUTOBJ02 = 301,
CUTOBJ03 = 302,
CUTOBJ04 = 303,
CUTOBJ05 = 304,
CUTOBJ06 = 305,
CUTOBJ07 = 306,
CUTOBJ08 = 307,
CUTOBJ09 = 308,
CUTOBJ10 = 309,
CUTOBJ11 = 310,
CUTOBJ12 = 311,
CUTOBJ13 = 312,
CUTOBJ14 = 313,
CUTOBJ15 = 314,
CUTOBJ16 = 315,
CUTOBJ17 = 316,
CUTOBJ18 = 317,
CUTOBJ19 = 318,
CUTOBJ20 = 319,
---
-- vehicles.ide
---
LANDSTAL = 400,
BRAVURA = 401,
BUFFALO = 402,
LINERUN = 403,
PEREN = 404,
SENTINEL = 405,
DUMPER = 406,
FIRETRUK = 407,
TRASH = 408,
STRETCH = 409,
MANANA = 410,
INFERNUS = 411,
VOODOO = 412,
PONY = 413,
MULE = 414,
CHEETAH = 415,
AMBULAN = 416,
LEVIATHN = 417,
MOONBEAM = 418,
ESPERANT = 419,
TAXI = 420,
WASHING = 421,
BOBCAT = 422,
MRWHOOP = 423,
BFINJECT = 424,
HUNTER = 425,
PREMIER = 426,
ENFORCER = 427,
SECURICA = 428,
BANSHEE = 429,
PREDATOR = 430,
BUS = 431,
RHINO = 432,
BARRACKS = 433,
HOTKNIFE = 434,
ARTICT1 = 435,
PREVION = 436,
COACH = 437,
CABBIE = 438,
STALLION = 439,
RUMPO = 440,
RCBANDIT = 441,
ROMERO = 442,
PACKER = 443,
MONSTER = 444,
ADMIRAL = 445,
SQUALO = 446,
SEASPAR = 447,
PIZZABOY = 448,
TRAM = 449,
ARTICT2 = 450,
TURISMO = 451,
SPEEDER = 452,
REEFER = 453,
TROPIC = 454,
FLATBED = 455,
YANKEE = 456,
CADDY = 457,
SOLAIR = 458,
TOPFUN = 459,
SKIMMER = 460,
PCJ600 = 461,
FAGGIO = 462,
FREEWAY = 463,
RCBARON = 464,
RCRAIDER = 465,
GLENDALE = 466,
OCEANIC = 467,
SANCHEZ = 468,
SPARROW = 469,
PATRIOT = 470,
QUAD = 471,
COASTG = 472,
DINGHY = 473,
HERMES = 474,
SABRE = 475,
RUSTLER = 476,
ZR350 = 477,
WALTON = 478,
REGINA = 479,
COMET = 480,
BMX = 481,
BURRITO = 482,
CAMPER = 483,
MARQUIS = 484,
BAGGAGE = 485,
DOZER = 486,
MAVERICK = 487,
VCNMAV = 488,
RANCHER = 489,
FBIRANCH = 490,
VIRGO = 491,
GREENWOO = 492,
JETMAX = 493,
HOTRING = 494,
SANDKING = 495,
BLISTAC = 496,
POLMAV = 497,
BOXVILLE = 498,
BENSON = 499,
MESA = 500,
RCGOBLIN = 501,
HOTRINA = 502,
HOTRINB = 503,
BLOODRA = 504,
RNCHLURE = 505,
SUPERGT = 506,
ELEGANT = 507,
JOURNEY = 508,
BIKE = 509,
MTBIKE = 510,
BEAGLE = 511,
CROPDUST = 512,
STUNT = 513,
PETRO = 514,
RDTRAIN = 515,
NEBULA = 516,
MAJESTIC = 517,
BUCCANEE = 518,
SHAMAL = 519,
HYDRA = 520,
FCR900 = 521,
NRG500 = 522,
COPBIKE = 523,
CEMENT = 524,
TOWTRUCK = 525,
FORTUNE = 526,
CADRONA = 527,
FBITRUCK = 528,
WILLARD = 529,
FORKLIFT = 530,
TRACTOR = 531,
COMBINE = 532,
FELTZER = 533,
REMINGTN = 534,
SLAMVAN = 535,
BLADE = 536,
FREIGHT = 537,
STREAK = 538,
VORTEX = 539,
VINCENT = 540,
BULLET = 541,
CLOVER = 542,
SADLER = 543,
FIRELA = 544,
HUSTLER = 545,
INTRUDER = 546,
PRIMO = 547,
CARGOBOB = 548,
TAMPA = 549,
SUNRISE = 550,
MERIT = 551,
UTILITY = 552,
NEVADA = 553,
YOSEMITE = 554,
WINDSOR = 555,
MONSTERA = 556,
MONSTERB = 557,
URANUS = 558,
JESTER = 559,
SULTAN = 560,
STRATUM = 561,
ELEGY = 562,
RAINDANC = 563,
RCTIGER = 564,
FLASH = 565,
TAHOMA = 566,
SAVANNA = 567,
BANDITO = 568,
FREIFLAT = 569,
STREAKC = 570,
KART = 571,
MOWER = 572,
DUNERIDE = 573,
SWEEPER = 574,
BROADWAY = 575,
TORNADO = 576,
AT400 = 577,
DFT30 = 578,
HUNTLEY = 579,
STAFFORD = 580,
BF400 = 581,
NEWSVAN = 582,
TUG = 583,
PETROTR = 584,
EMPEROR = 585,
WAYFARER = 586,
EUROS = 587,
HOTDOG = 588,
CLUB = 589,
FREIBOX = 590,
ARTICT3 = 591,
ANDROM = 592,
DODO = 593,
RCCAM = 594,
LAUNCH = 595,
COPCARLA = 596,
COPCARSF = 597,
COPCARVG = 598,
COPCARRU = 599,
PICADOR = 600,
SWATVAN = 601,
ALPHA = 602,
PHOENIX = 603,
GLENSHIT = 604,
SADLSHIT = 605,
BAGBOXA = 606,
BAGBOXB = 607,
TUGSTAIR = 608,
BOXBURG = 609,
FARMTR1 = 610,
UTILTR1 = 611,
---
-- peds.ide
---
NULL = 0,
MALE01 = 7,
BFORI = 9,
BFOST = 10,
VBFYCRP = 11,
BFYRI = 12,
BFYST = 13,
BMORI = 14,
BMOST = 15,
BMYAP = 16,
BMYBU = 17,
BMYBE = 18,
BMYDJ = 19,
BMYRI = 20,
BMYCR = 21,
BMYST = 22,
WMYBMX = 23,
WBDYG1 = 24,
WBDYG2 = 25,
WMYBP = 26,
WMYCON = 27,
BMYDRUG = 28,
WMYDRUG = 29,
HMYDRUG = 30,
DWFOLC = 31,
DWMOLC1 = 32,
DWMOLC2 = 33,
DWMYLC1 = 34,
HMOGAR = 35,
WMYGOL1 = 36,
WMYGOL2 = 37,
HFORI = 38,
HFOST = 39,
HFYRI = 40,
HFYST = 41,
HMORI = 43,
HMOST = 44,
HMYBE = 45,
HMYRI = 46,
HMYCR = 47,
HMYST = 48,
OMOKUNG = 49,
WMYMECH = 50,
BMYMOUN = 51,
WMYMOUN = 52,
OFORI = 53,
OFOST = 54,
OFYRI = 55,
OFYST = 56,
OMORI = 57,
OMOST = 58,
OMYRI = 59,
OMYST = 60,
WMYPLT = 61,
WMOPJ = 62,
BFYPRO = 63,
HFYPRO = 64,
BMYPOL1 = 66,
BMYPOL2 = 67,
WMOPREA = 68,
SBFYST = 69,
WMOSCI = 70,
WMYSGRD = 71,
SWMYHP1 = 72,
SWMYHP2 = 73,
SWFOPRO = 75,
WFYSTEW = 76,
SWMOTR1 = 77,
WMOTR1 = 78,
BMOTR1 = 79,
VBMYBOX = 80,
VWMYBOX = 81,
VHMYELV = 82,
VBMYELV = 83,
VIMYELV = 84,
VWFYPRO = 85,
VWFYST1 = 87,
WFORI = 88,
WFOST = 89,
WFYJG = 90,
WFYRI = 91,
WFYRO = 92,
WFYST = 93,
WMORI = 94,
WMOST = 95,
WMYJG = 96,
WMYLG = 97,
WMYRI = 98,
WMYRO = 99,
WMYCR = 100,
WMYST = 101,
BALLAS1 = 102,
BALLAS2 = 103,
BALLAS3 = 104,
FAM1 = 105,
FAM2 = 106,
FAM3 = 107,
LSV1 = 108,
LSV2 = 109,
LSV3 = 110,
MAFFA = 111,
MAFFB = 112,
MAFBOSS = 113,
VLA1 = 114,
VLA2 = 115,
VLA3 = 116,
TRIADA = 117,
TRIADB = 118,
TRIBOSS = 120,
DNB1 = 121,
DNB2 = 122,
DNB3 = 123,
VMAFF1 = 124,
VMAFF2 = 125,
VMAFF3 = 126,
VMAFF4 = 127,
DNMYLC = 128,
DNFOLC1 = 129,
DNFOLC2 = 130,
DNFYLC = 131,
DNMOLC1 = 132,
DNMOLC2 = 133,
SBMOTR2 = 134,
SWMOTR2 = 135,
SBMYTR3 = 136,
SWMOTR3 = 137,
WFYBE = 138,
BFYBE = 139,
HFYBE = 140,
SOFYBU = 141,
SBMYST = 142,
SBMYCR = 143,
BMYCG = 144,
WFYCRK = 145,
HMYCM = 146,
WMYBU = 147,
BFYBU = 148,
WFYBU = 150,
DWFYLC1 = 151,
WFYPRO = 152,
WMYCONB = 153,
WMYBE = 154,
WMYPIZZ = 155,
BMOBAR = 156,
CWFYHB = 157,
CWMOFR = 158,
CWMOHB1 = 159,
CWMOHB2 = 160,
CWMYFR = 161,
CWMYHB1 = 162,
BMYBOUN = 163,
WMYBOUN = 164,
WMOMIB = 165,
BMYMIB = 166,
WMYBELL = 167,
BMOCHIL = 168,
SOFYRI = 169,
SOMYST = 170,
VWMYBJD = 171,
VWFYCRP = 172,
SFR1 = 173,
SFR2 = 174,
SFR3 = 175,
BMYBAR = 176,
WMYBAR = 177,
WFYSEX = 178,
WMYAMMO = 179,
BMYTATT = 180,
VWMYCR = 181,
VBMOCD = 182,
VBMYCR = 183,
VHMYCR = 184,
SBMYRI = 185,
SOMYRI = 186,
SOMYBU = 187,
SWMYST = 188,
WMYVA = 189,
COPGRL3 = 190,
GUNGRL3 = 191,
MECGRL3 = 192,
NURGRL3 = 193,
CROGRL3 = 194,
GANGRL3 = 195,
CWFOFR = 196,
CWFOHB = 197,
CWFYFR1 = 198,
CWFYFR2 = 199,
CWMYHB2 = 200,
DWFYLC2 = 201,
DWMYLC2 = 202,
OMYKARA = 203,
WMYKARA = 204,
WFYBURG = 205,
VWMYCD = 206,
VHFYPRO = 207,
OMONOOD = 209,
OMOBOAT = 210,
WFYCLOT = 211,
VWMOTR1 = 212,
VWMOTR2 = 213,
VWFYWAI = 214,
SBFORI = 215,
SWFYRI = 216,
WMYCLOT = 217,
SBFOST = 218,
SBFYRI = 219,
SBMOCD = 220,
SBMORI = 221,
SBMOST = 222,
SHMYCR = 223,
SOFORI = 224,
SOFOST = 225,
SOFYST = 226,
SOMOBU = 227,
SOMORI = 228,
SOMOST = 229,
SWMOTR5 = 230,
SWFORI = 231,
SWFOST = 232,
SWFYST = 233,
SWMOCD = 234,
SWMORI = 235,
SWMOST = 236,
SHFYPRO = 237,
SBFYPRO = 238,
SWMOTR4 = 239,
SWMYRI = 240,
SMYST = 241,
SMYST2 = 242,
SFYPRO = 243,
VBFYST2 = 244,
VBFYPRO = 245,
VHFYST3 = 246,
BIKERA = 247,
BIKERB = 248,
BMYPIMP = 249,
SWMYCR = 250,
WFYLG = 251,
WMYVA2 = 252,
BMOSEC = 253,
BIKDRUG = 254,
WMYCH = 255,
SBFYSTR = 256,
SWFYSTR = 257,
HECK1 = 258,
HECK2 = 259,
BMYCON = 260,
WMYCD1 = 261,
BMOCD = 262,
VWFYWA2 = 263,
WMOICE = 264,
LAEMT1 = 274,
LVEMT1 = 275,
SFEMT1 = 276,
LAFD1 = 277,
LVFD1 = 278,
SFFD1 = 279,
LAPD1 = 280,
SFPD1 = 281,
LVPD1 = 282,
CSHER = 283,
LAPDM1 = 284,
SWAT = 285,
FBI = 286,
ARMY = 287,
DSHER = 288,
SPECIAL01 = 290,
SPECIAL02 = 291,
SPECIAL03 = 292,
SPECIAL04 = 293,
SPECIAL05 = 294,
SPECIAL06 = 295,
SPECIAL07 = 296,
SPECIAL08 = 297,
SPECIAL09 = 298,
SPECIAL10 = 299,
---
-- veh_mods.ide
---
SPL_B_MAR_M = 1000,
SPL_B_BAB_M = 1001,
SPL_B_BAR_M = 1002,
SPL_B_MAB_M = 1003,
BNT_B_SC_M = 1004,
BNT_B_SC_L = 1005,
RF_B_SC_R = 1006,
WG_L_B_SSK = 1007,
NTO_B_L = 1008,
NTO_B_S = 1009,
NTO_B_TW = 1010,
BNT_B_SC_P_M = 1011,
BNT_B_SC_P_L = 1012,
LGT_B_RSPT = 1013,
SPL_B_BAR_L = 1014,
SPL_B_BBR_L = 1015,
SPL_B_BBR_M = 1016,
WG_R_B_SSK = 1017,
EXH_B_TS = 1018,
EXH_B_T = 1019,
EXH_B_L = 1020,
EXH_B_M = 1021,
EXH_B_S = 1022,
SPL_B_BBB_M = 1023,
LGT_B_SSPT = 1024,
WHEEL_OR1 = 1025,
WG_L_A_S = 1026,
WG_R_A_S = 1027,
EXH_A_S = 1028,
EXH_C_S = 1029,
WG_R_C_S = 1030,
WG_L_C_S = 1031,
RF_A_S = 1032,
RF_C_S = 1033,
EXH_A_L = 1034,
RF_C_L = 1035,
WG_L_A_L = 1036,
EXH_C_L = 1037,
RF_A_L = 1038,
WG_L_C_L = 1039,
WG_R_A_L = 1040,
WG_R_C_L = 1041,
WG_L_LR_BR1 = 1042,
EXH_LR_BR2 = 1043,
EXH_LR_BR1 = 1044,
EXH_C_F = 1045,
EXH_A_F = 1046,
WG_L_A_F = 1047,
WG_L_C_F = 1048,
SPL_A_F_R = 1049,
SPL_C_F_R = 1050,
WG_R_A_F = 1051,
WG_R_C_F = 1052,
RF_C_F = 1053,
RF_A_F = 1054,
RF_A_ST = 1055,
WG_L_A_ST = 1056,
WG_L_C_ST = 1057,
SPL_A_ST_R = 1058,
EXH_C_ST = 1059,
SPL_C_ST_R = 1060,
RF_C_ST = 1061,
WG_R_A_ST = 1062,
WG_R_C_ST = 1063,
EXH_A_ST = 1064,
EXH_A_J = 1065,
EXH_C_J = 1066,
RF_A_J = 1067,
RF_C_J = 1068,
WG_L_A_J = 1069,
WG_L_C_J = 1070,
WG_R_A_J = 1071,
WG_R_C_J = 1072,
WHEEL_SR6 = 1073,
WHEEL_SR3 = 1074,
WHEEL_SR2 = 1075,
WHEEL_LR4 = 1076,
WHEEL_LR1 = 1077,
WHEEL_LR3 = 1078,
WHEEL_SR1 = 1079,
WHEEL_SR5 = 1080,
WHEEL_SR4 = 1081,
WHEEL_GN1 = 1082,
WHEEL_LR2 = 1083,
WHEEL_LR5 = 1084,
WHEEL_GN2 = 1085,
STEREO = 1086,
HYDRALICS = 1087,
RF_A_U = 1088,
EXH_C_U = 1089,
WG_L_A_U = 1090,
RF_C_U = 1091,
EXH_A_U = 1092,
WG_L_C_U = 1093,
WG_R_A_U = 1094,
WG_R_C_U = 1095,
WHEEL_GN3 = 1096,
WHEEL_GN4 = 1097,
WHEEL_GN5 = 1098,
WG_R_LR_BR1 = 1099,
MISC_C_LR_REM1 = 1100,
WG_R_LR_REM1 = 1101,
WG_R_LR_SV = 1102,
RF_LR_BL2 = 1103,
EXH_LR_BL1 = 1104,
EXH_LR_BL2 = 1105,
WG_L_LR_REM2 = 1106,
WG_R_LR_BL1 = 1107,
WG_L_LR_BL1 = 1108,
BBB_LR_SLV1 = 1109,
BBB_LR_SLV2 = 1110,
BNT_LR_SLV1 = 1111,
BNT_LR_SLV2 = 1112,
EXH_LR_SLV1 = 1113,
EXH_LR_SLV2 = 1114,
FBB_LR_SLV1 = 1115,
FBB_LR_SLV2 = 1116,
FBMP_LR_SLV1 = 1117,
WG_L_LR_SLV1 = 1118,
WG_L_LR_SLV2 = 1119,
WG_R_LR_SLV1 = 1120,
WG_R_LR_SLV2 = 1121,
WG_L_LR_REM1 = 1122,
MISC_C_LR_REM2 = 1123,
WG_R_LR_REM2 = 1124,
MISC_C_LR_REM3 = 1125,
EXH_LR_REM1 = 1126,
EXH_LR_REM2 = 1127,
RF_LR_BL1 = 1128,
EXH_LR_SV1 = 1129,
RF_LR_SV1 = 1130,
RF_LR_SV2 = 1131,
EXH_LR_SV2 = 1132,
WG_L_LR_SV = 1133,
WG_L_LR_T1 = 1134,
EXH_LR_T2 = 1135,
EXH_LR_T1 = 1136,
WG_R_LR_T1 = 1137,
SPL_A_S_B = 1138,
SPL_C_S_B = 1139,
RBMP_C_S = 1140,
RBMP_A_S = 1141,
BNTR_B_OV = 1142,
BNTL_B_OV = 1143,
BNTR_B_SQ = 1144,
BNTL_B_SQ = 1145,
SPL_C_L_B = 1146,
SPL_A_L_B = 1147,
RBMP_C_L = 1148,
RBMP_A_L = 1149,
RBMP_A_F = 1150,
RBMP_C_F = 1151,
FBMP_C_F = 1152,
FBMP_A_F = 1153,
RBMP_A_ST = 1154,
FBMP_A_ST = 1155,
RBMP_C_ST = 1156,
FBMP_C_ST = 1157,
SPL_C_J_B = 1158,
RBMP_A_J = 1159,
FBMP_A_J = 1160,
RBMP_C_J = 1161,
SPL_A_J_B = 1162,
SPL_C_U_B = 1163,
SPL_A_U_B = 1164,
FBMP_C_U = 1165,
FBMP_A_U = 1166,
RBMP_C_U = 1167,
RBMP_A_U = 1168,
FBMP_A_S = 1169,
FBMP_C_S = 1170,
FBMP_A_L = 1171,
FBMP_C_L = 1172,
FBMP_C_J = 1173,
FBMP_LR_BR1 = 1174,
FBMP_LR_BR2 = 1175,
RBMP_LR_BR1 = 1176,
RBMP_LR_BR2 = 1177,
RBMP_LR_REM2 = 1178,
FBMP_LR_REM1 = 1179,
RBMP_LR_REM1 = 1180,
FBMP_LR_BL2 = 1181,
FBMP_LR_BL1 = 1182,
RBMP_LR_BL2 = 1183,
RBMP_LR_BL1 = 1184,
FBMP_LR_REM2 = 1185,
RBMP_LR_SV2 = 1186,
RBMP_LR_SV1 = 1187,
FBMP_LR_SV2 = 1188,
FBMP_LR_SV1 = 1189,
FBMP_LR_T2 = 1190,
FBMP_LR_T1 = 1191,
RBMP_LR_T1 = 1192,
RBMP_LR_T2 = 1193
}
