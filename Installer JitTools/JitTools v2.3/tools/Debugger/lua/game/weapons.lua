-- This file is part of SA MoonLoader package.
-- Licensed under the MIT License.
-- Copyright (c) 2016, BlastHack Team <blast.hk>


local weapons = {
	FIST = 0,
	BRASSKNUCKLES = 1,
	GOLFCLUB = 2,
	NIGHTSTICK = 3,
	KNIFE = 4,
	BASEBALLBAT = 5,
	SHOVEL = 6,
	PO<PERSON>CUE = 7,
	KATANA = 8,
	CHAINSAW = 9,
	PURPLEDILDO = 10,
	WHITEDILDO = 11,
	WHITEVIBRATOR = 12,
	SILVERVIBRATOR = 13,
	FLOWERS = 14,
	CANE = 15,
	GRENADE = 16,
	TEARGAS = 17,
	MOLOTOV = 18,
	COLT45 = 22,
	SILENCED = 23,
	DESERTEAGLE = 24,
	SHOTGUN = 25,
	SAWNOFFSHOTGUN = 26,
	COMBATSHOTGUN = 27,
	UZI = 28,
	MP5 = 29,
	AK47 = 30,
	M4 = 31,
	TEC9 = 32,
	RIFLE = 33,
	SNIPERRIFLE = 34,
	ROCKETLAUNCHER = 35,
	HEATSEEKER = 36,
	<PERSON>AMETH<PERSON>WER = 37,
	MI<PERSON><PERSON><PERSON> = 38,
	<PERSON>CH<PERSON><PERSON>AR<PERSON> = 39,
	<PERSON><PERSON><PERSON><PERSON><PERSON> = 40,
	SPRAYCAN = 41,
	FIREE<PERSON>INGUISHER = 42,
	CA<PERSON><PERSON> = 43,
	<PERSON><PERSON>HTVISION = 44,
	THERMALVISION = 45,
	PARACHUTE = 46
}

local id = weapons
weapons.names = {
	[id.FIST] = 'Fist',
	[id.BRASSKNUCKLES] = 'Brass Knuckles',
	[id.GOLFCLUB] = 'Golf Club',
	[id.NIGHTSTICK] = 'Nightstick',
	[id.KNIFE] = 'Knife',
	[id.BASEBALLBAT] = 'Baseball Bat',
	[id.SHOVEL] = 'Shovel',
	[id.POOLCUE] = 'Pool Cue',
	[id.KATANA] = 'Katana',
	[id.CHAINSAW] = 'Chainsaw',
	[id.PURPLEDILDO] = 'Purple Dildo',
	[id.WHITEDILDO] = 'Dildo',
	[id.WHITEVIBRATOR] = 'Vibrator',
	[id.SILVERVIBRATOR] = 'Silver Vibrator',
	[id.FLOWERS] = 'Flowers',
	[id.CANE] = 'Cane',
	[id.GRENADE] = 'Grenade',
	[id.TEARGAS] = 'Tear Gas',
	[id.MOLOTOV] = 'Molotov Cocktail',
	[id.COLT45] = '9mm',
	[id.SILENCED] = 'Silenced 9mm',
	[id.DESERTEAGLE] = 'Desert Eagle',
	[id.SHOTGUN] = 'Shotgun',
	[id.SAWNOFFSHOTGUN] = 'Sawnoff Shotgun',
	[id.COMBATSHOTGUN] = 'Combat Shotgun',
	[id.UZI] = 'Micro Uzi',
	[id.MP5] = 'MP5',
	[id.AK47] = 'AK-47',
	[id.M4] = 'M4',
	[id.TEC9] = 'Tec-9',
	[id.RIFLE] = 'Country Rifle',
	[id.SNIPERRIFLE] = 'Sniper Rifle',
	[id.ROCKETLAUNCHER] = 'RPG',
	[id.HEATSEEKER] = 'HS Rocket',
	[id.FLAMETHROWER] = 'Flamethrower',
	[id.MINIGUN] = 'Minigun',
	[id.SATCHELCHARGE] = 'Satchel Charge',
	[id.DETONATOR] = 'Detonator',
	[id.SPRAYCAN] = 'Spraycan',
	[id.FIREEXTINGUISHER] = 'Fire Extinguisher',
	[id.CAMERA] = 'Camera',
	[id.NIGHTVISION] = 'Night Vis Goggles',
	[id.THERMALVISION] = 'Thermal Goggles',
	[id.PARACHUTE] = 'Parachute'
}

function weapons.get_name(id)
	return weapons.names[id]
end

return weapons
