-- This file is part of SA MoonLoader package.
-- Licensed under the MIT License.
-- Copyright (c) 2016, BlastHack Team <blast.hk>

-- From Sanny Builder 3

return {
-------------------------------
-- $0 - $99
-------------------------------
-- 0 = N/A
-- 1 = N/A
PLAYER_CHAR = 2,
PLAYER_ACTOR = 3,
-- 4= gymbike float
-- 5= useless= used once
-- 6= gymbike temp float
PLAYER_GROUP = 11,
-- 12= BSKOOL flag
-- 13= Current_Controls
DEFAULT_WAIT_TIME = 14,
Phone_Ringing_Flag = 15,
Current_Time_in_ms2 = 16,
Move_Axis_X = 17,
Move_Axis_Y = 18,
Special_Axis_X = 19,
Special_Axis_Y = 20,
Help_Bicycle_1_Shown = 21,
Marker_Trucking_Mission = 22,
-- 23= useless, equals to 0
Mission_Intro_Passed = 24,
STAT_Unlocked_Cities_Number = 25,
Shooting_Available = 26,
Tags_Painted_Percentage = 27,
-- 28= actor ever ate a food flag?
Mission_Running_Dog_Failed = 29,
-- 30= useless= used once
Marker_Emmets_Gun = 31,
Pickup_Emmets_Colt45 = 32,
STAT_Lung_Capacity = 33,
Time_Hours = 34,
Time_Mins = 35,
-- 36= useless?
-- 37= flag_Got_Millie_Keycard
-- 38= unknown ENTEXT flag
Time_From_Last_Call_Got_Flag = 39,
Weekday = 40,
-- 41= useless IMPEXPM flag
Flight_School_Intro_Flag = 42,
Active_Interior = 43,
-- 44= Marker Triads SF Garage created flag
Selected_Menu_Item = 45,
-- 46= Marker CRASH SF Garage created flag
-- 47= get respect?
tempvar_Actor_Car = 48,
tempvar_Car_Type = 49,
Help_Valet_Shown = 50,
Help_Denise_Shown = 51,
Help_Stealth_Shown = 52,
-- 53= unknown DSKOOL
Help_INTRO_Shown = 54,
-- 55= Marker_Asset created
-- 56= useless
LS_Final_Missions_Started = 57,
-- 58= Riot_Missions_Started
-- 59= Marker SFG created
-- 60= car INTRO
-- 61= marker above car INTRO
Help_Trucker_MIssions_Shown = 62,
-- 63= R3_Player_Car
Catalina_Total_Passed_Missions = 64,
Help_Groove_Shown = 65,
Marker_Wang_Cars = 66,
AMMU_Icons_Shown = 67,
Distance_Between_Points = 68,
tempvar_X_coord = 69,
tempvar_Y_coord = 70,
tempvar_Z_coord = 71,
tempvar_Angle = 72,
tempvar_Float_1 = 73,
tempvar_Float_2 = 74,
tempvar_Float_3 = 75,
-- 76= useless float= used once
-- 77= useless float= used once
-- 78= useless float= used once
-- 79= useless float= used once
-- 80= useless float= used once
-- 81= useless float= used once
Help_Bicycle_2_Shown = 82,
Help_DriveBy_Shown = 83,
Help_Follow_Icon_Shown = 84,
Help_Camera_and_Radio_Shown = 85,
Mission_Back_to_School_Passed = 86,
Mission_Learning_to_Fly_Passed = 87,
-- 88= unknown INTRO2 stage
-- 89= unknown MAIN stage
Mission_Kickstart_Passed = 90,
-- 91= Driving Level Score
-- 92= Driving Level Score
-- 93= useless= used once
-- 94= Driving Level Score
-- 95= useless= used once
-- 96= Driving Level Score
-- 97= Driving Level Score
-- 98= Driving Level Score
-- 99= useless= used once
-------------------------------
-- $100 - $199
-------------------------------
-- 100= Alley Oop Score
Help_SF_Photos_Shown = 106,
Help_Wasted_Shown = 119,
Custom_Tournament_Flag = 121,
LSGym_Icon = 122,
STAT_Cycling_Skill = 123,
Pickup_Info_Conversation = 124,
Help_Chat_Shown = 125,
Help_Busted_Shown = 126,
Show_Shop_Sphere = 128,
Actor_Smoke = 130,
Actor_Sweet = 131,
Actor_Ryder = 132,
Actor_Cesar = 133,
Actor_OG_Loc = 134,
Smoke_Car = 135,
Sweet_Car = 136,
Ryder_Car = 137,
Actor_Truth = 146,
Actor_Catalina = 147,
ONMISSION_Paramedic = 148,
ONMISSION_Firefighter = 149,
Help_Firetruck_Shown = 150,
-- 151=Mission_End_Of_The_Line_Stage
ONMISSION_Stunt_BMX = 152,
ONMISSION_Stunt_NRG500 = 153,
Current_Language = 154,
SECOND_PLAYER = 155,
SECOND_PLAYER_ACTOR = 156,
-- 159= useless
-- 160= useless
ONMISSION_Burglary = 165,
-- 166= useless
-- 167= useless
-- 168= useless
ONMISSION_Chiliad = 175,
Total_Passengers_DroppedOff = 180,
Help_Toggle_OddJob_Shown = 181,
ONMISSION_TAXI = 182,
Current_Time_in_ms = 184,
TempVar_Call_Time = 185,
Time_From_Last_Call = 186,
STAT_Stamina = 187,
ONMISSION_Courier = 189,
LOWR_CONT_Script_Launched = 194,
Mission_Cesar_Vialpando_Passed = 195,
Help_Croupier_Uniform_Shown = 199,
-------------------------------
-- $200 - $299
-------------------------------
Help_Swim_Shown = 248,
Current_Flight_Record = 271,
-------------------------------
-- $300 - $399
-------------------------------
Race_Index = 352,
-- array 6 $353-$358
Girl_Desired_Progress_To_Invite = 353,
Girl_Progress = 359,
-- array 6 $365-$370
Girl_Desired_SexAppeal = 365,
-- 371=Girl_Bitmask1
-- array 6 $377-$382
-- 377=Girl_Bitmask2
-- array 6 $383-$388
-- 383=Girl_Bitmask3
Girlfriend = 389,
Girl_Dated_Now = 393,
-- array 6i $398-$403
GirlMarker = 398,
-------------------------------
-- $400 - $499
-------------------------------
Girls_Gifts_bitmask = 406,
GYM_STAT_Fat = 407,
Garage_BODLAWN_Icon = 408,
ONMISSION = 409,
-- 409=on_mission
Help_SprayCan_Shown = 410,
Garage_MODLAST_Icon = 414,
BCESAR2_Script_Launched = 417,
BCESAR3_Script_Launched = 418,
-- 419= useless= used once
-- 420= useless= used once
-- 421= useless= used once
X_Pershing_Square_LS = 422,
Y_Pershing_Square_LS = 423,
Z_Pershing_Square_LS = 424,
Marker_Sweet_House = 435,
Marker_Crash_LS = 438,
Marker_Cesar_LS = 440,
icon_CJ = 441,
icon_Sweet = 442,
icon_Ryder = 443,
icon_Big_Dmoke = 445,
icon_OG_Loc = 446,
icon_Crash = 444,
icon_Cesar = 447,
Intro_Total_Passed_Missions = 448,
Sweet_Total_Passed_Missions = 452,
Ryder_Total_Passed_Missions = 453,
Smoke_Total_Passed_Missions = 454,
OG_Loc_Total_Passed_Missions = 455,
CRASH_LS_Total_Passed_Missions = 456,
Mission_Lowrider_Passed = 457,
LS_FINAL_Total_Passed_Missions = 458,
X_Johnson_house = 459,
Y_Johnson_house = 460,
Z_Johnson_house = 461,
X_Sweet_House = 462,
Y_Sweet_House = 463,
Z_Sweet_House = 464,
X_Ryder_House = 465,
Y_Ryder_House = 466,
Z_Ryder_house = 467,
X_Big_Smoke_House = 468,
Y_Big_Smoke_House = 469,
Z_Big_Smoke_House = 470,
X_OG_Loc_Work = 471,
Y_OG_Loc_Work = 472,
Z_OG_Loc_Work = 473,
X_OG_Loc_House = 474,
Y_OG_Loc_House = 475,
Z_OG_Loc_House = 476,
X_CRASH_LS = 477,
Y_CRASH_LS = 478,
Z_CRASH_LS = 479,
X_Cesar_house = 480,
Y_Cesar_house = 481,
Z_Cesar_house = 482,
Marker_Catalina = 483,
Marker_Cesar_Montgomery = 485,
Marker_Truth_Farm = 486,
icon_Catalina = 487,
icon_Truth = 488,
Truth_Total_Passed_Missions = 491,
Cesar_Total_Passed_Missions = 492,
Mission_Badlands_Passed = 493,
X_Angel_Pine = 494,
Y_Angel_Pine = 495,
Z_Angel_Pine = 496,
X_Truth_Farm = 497,
Y_Truth_Farm = 498,
Z_Truth_Farm = 499,
-------------------------------
-- $500 - $599
-------------------------------
X_Angel_Pine_Motel = 500,
Y_Angel_Pine_Motel = 501,
Z_Angel_Pine_Motel = 502,
X_Montgomery = 503,
Y_Montgomery = 504,
Z_Montgomery = 505,
X_Catalina_House = 506,
X_Local_Liquor_Store = 507,
X_Small_Town_Bank = 508,
X_Tanker_Commander = 509,
X_Against_All_Odds = 510,
X_Meeting_Catalina = 511,
Y_Catalina_House = 512,
Y_Local_Liquor_Store = 513,
Y_Small_Town_Bank = 514,
Y_Tanker_Commander = 515,
Y_Against_All_Odds = 516,
Y_Meeting_Catalina = 517,
Z_Catalina_House = 518,
Z_Local_Liquor_Store = 519,
Z_Small_Town_Bank = 520,
Z_Tanker_Commander = 521,
Z_Against_All_Odds = 522,
Z_Meeting_Catalina = 523,
--
Marker_Triads_SF_Garage = 533,
icon_Woozie = 534,
icon_LocoSyndicate = 535,
icon_Cesar_2 = 536,
icon_Triads = 537,
icon_Race = 538,
icon_Zero = 539,
--
Garage_Total_Passed_Missions = 541,
Zero_Total_Passed_Missions = 542,
Wuzimu_Total_Passed_Missions = 543,
Steal_Total_Passed_Missions = 544,
Syndicate_Total_Passed_Missions = 545,
CRASH_SF_Total_Passed_Missions = 546,
X_SF_garage = 547,
Y_SF_garage = 548,
Z_SF_garage = 549,
X_Zero_RC_shop = 550,
Y_Zero_RC_shop = 551,
Z_Zero_RC_shop = 552,
X_Woozie_House = 553,
Y_Woozie_House = 554,
Z_Woozie_House = 555,
X_SF_garage_2 = 556,
Y_SF_garage_2 = 557,
Z_SF_garage_2 = 558,
X_Jizzy_Club = 559,
Y_Jizzy_Club = 560,
Z_Jizzy_Club = 561,
-- array 4 $562-$565
X_Race_Tournament_Point = 562,
-- array 4 $566-$569
Y_Race_Tournament_Point = 566,
-- array 4 $570-$573
Z_Race_Tournament_Point = 570,
X_SF_Garage_3 = 574,
Y_SF_Garage_3 = 575,
Z_SF_Garage_3 = 576,
X_Driving_School = 577,
Y_Driving_School = 578,
Z_Driving_School = 579,
--
icon_Toreno = 586,
icon_Airyard = 587,
icon_Triads_Casino = 588,
icon_MadDog = 589,
icon_Mafia_Casino = 590,
icon_School = 591,
Marker_Pilot_School = 592,
Toreno_Total_Passed_Missions = 593,
icon_Cash = 594,
Casino_Total_Passed_Missions = 597,
-------------------------------
-- $600 - $699
-------------------------------
Heist_Total_Passed_Missions = 600,
X_Tierra_Robada_Ranch = 601,
Y_Tierra_Robada_Ranch = 602,
Z_Tierra_Robada_Ranch = 603,
X_Old_Airport = 604,
Y_Old_Airport = 605,
Z_Old_Airport = 606,
X_The_Four_Dragons_Casino = 607,
Y_The_Four_Dragons_Casino = 608,
Z_The_Four_Dragons_Casino = 609,
X_The_Four_Dragons_Casino_2 = 610,
Y_The_Four_Dragons_Casino_2 = 611,
Z_The_Four_Dragons_Casino_2 = 612,
X_Prickle_Pine_LV = 613,
Y_Prickle_Pine_LV = 614,
Z_Prickle_Pine_LV = 615,
X_Royal_Casino = 616,
Y_Royal_Casino = 617,
Z_Royal_Casino = 618,
X_Caligula_Palace = 619,
Y_Caligula_Palace = 620,
Z_Caligula_Palace = 621,
icon_CJ_2 = 624,
icon_Sweet_2 = 625,
Mansion_Total_Passed_Missions = 626,
Grove_Total_Passed_Missions = 627,
Marker_Quarry_Mission = 628,
Riot_Total_Passed_Missions = 629,
Help_Gimp_Suit_Shown = 631,
X_Madd_Dogg_Crib = 633,
Y_Madd_Dogg_Crib = 634,
Z_Madd_Dogg_Crib = 635,
-- 636=useless= used once
-- 637=useless= used once
-- 638=useless= used once
Pickup_Info_Hospital = 669,
Pickup_Info_Hospital_2 = 670,
Pickup_Info_Police = 671,
parked_Hydra = 673,
Total_Food_Eaten = 674,
FOOD_Player_Aggressive_Flag = 677,
X_Buy_Marker = 678,
Y_Buy_Marker = 679,
Z_Buy_Marker = 680,
X_Start_Anim_Food = 681,
Y_Start_Anim_Food = 682,
Z_Start_Anim_Food = 683,
X_Camera = 686,
Y_Camera = 687,
Z_Camera = 688,
AMMU_X_Seller = 689,
AMMU_Y_Seller = 690,
AMMU_Z_Seller = 691,
-- 692-705= useless= used once -- unknown text_draw params (letters size, RGBA)
-------------------------------
-- $700 - $799
-------------------------------
Current_Shop_GXT_Name = 706,
-- 707= useless
Video_Game = 708,
-- 709= gates_open ?
GS_Gang_Money_Pickup = 710,
GS_Gang_Turf_Controlled = 711,
GS_Gang_Cash = 712,
Script_Status = 713,
Mission_Local_Liquor_Store_Passed = 714,
Mission_Small_Town_Bank_Passed = 715,
Mission_Tanker_Commander_Passed = 716,
All_Catalina_missions_passed = 717,
Catalina_Selected_Mission = 718,
GymFight_Interior = 720,
STAT_Driving_Skill = 721,
Help_TRI_Cycling_Shown = 722,
Help_TRI_Stamina_Shown = 723,
Help_BMX_Cycling_Shown = 724,
Help_TRI_NoCock_Shown = 725,
STUNT_Mission_Type = 726,
BUY_Index = 727,
-- array 32 $728-$759
Property_Bought_Flags = 728,
--
-- array 32 $760-$791
Property_Indexes = 760,
Collected_Oysters = 798,
Collected_Horshoes = 799,
-------------------------------
-- $800 - $899
-------------------------------
STAT_Taken_Photos = 800,
STAT_Tags_Sprayed = 801,
_100_Percent_Complete = 802,
STAT_Percentage_Completed = 815,
GS_Money_Pickup_Exists = 816,
Pickup_Keycard = 817,
Pickup_Keycard_Collected = 818,
-- array 18 $865-$882
Save_Pickups = 865,
Save_Pickups_Index = 883,
Save_Pickups_Exist = 884,
Total_Available_Save_Pickups = 885,
-- array 18 $886-$903
Save_Pickups_X = 886,
-------------------------------
-- $900 - $999 completed
-------------------------------
-- array 18 $904-$921
Save_Pickups_Y = 904,
-- array 18 $922-$939
Save_Pickups_Z = 922,
-- array 18 $940-$957
Save_Points_X = 940,
-- array 18 $958-$975
Save_Points_Y = 958,
-- array 18 $976-$993
Save_Points_Z = 976,
-- array 18 $994-$1011
Save_Points_Angle = 994,
-------------------------------
-- $1000 - $1999
-------------------------------
LOWR_Player_Points = 1018,
LOWR_Opposition_Points = 1019,
LOWR_Last_Displayed_Text_ID = 1030,
Audio_Line_Is_Active = 1031,
Actor_Speech_GXT_Reference = 1032,
Actor_Speech_WAV_File = 1034,
Current_Wanted_List = 1049,
Import_Car = 1118,
Import_Car_Price = 1119,
-- array 10 $1020-$1029
WantedCrossOFF_Objects = 1120,
-- useless array 10 $1030-$1039
Wanted_List_Object = 1147,
-- array 10 $1050-$1059
Car_Models_To_Export = 1050,
-- array 10 $1060-$1069
Export_Cars_Status = 1060,
-- array 6 $1070-$1075
Import_Cars_Model = 1070,
-- array 42 $1076-$1117
Unlocked_Import_Cars = 1076,
-- 1146=useless IMPEXPM flag
Import_Panel_Row = 1149,
Import_Model_Name = 1151,
Import_Car_Panel = 1154,
-- array 7s $1155-$1167
Import_Car_Names = 1155,
-- array 7s $1067-$1180
Import_Days = 1167,
-- 1181=useless IMPEXPM
Import_New_Available_Cars_Number = 1182,
Help_Import_Shown = 1183,
All_Cars_Collected_Flag = 1184,
Import_Price_Multiplier = 1186,
Export_Price_Multiplier = 1187,
Export_Price_Health_Multiplier = 1188,
-- array 5 $1189-$1193
Import_Car_Handles = 1189,
Airport_Open_Flag = 1203,
-- array 6 $1204-$1209
GirlRespect = 1204,
GF_WAIT_TIME = 1212,
GFSEX_Excitement = 1217,
GFSEX_Power = 1218,
GF_Censore_Flag = 1219,
GF_Anim_Time = 1220,
GirlDoors = 1242,
PLAYER_IN_INTERIOR = 1249,
Active_Interior_Name = 1252,
Interior_Decision_Maker_A = 1256,
Interior_Decision_Maker_B = 1257,
Player_Wanted_Level = 1259,
HJ_Bonus = 1289,
HJ_Player_Car = 1290,
HJ_Begin_Stunt_Flag = 1291,
HJ_Stunt_Height = 1292,
HJ_Stunt_Bonus_Mult = 1294,
HJ_Stunt_Flipping_Flag = 1295,
HJ_Stunt_Flips = 1296,
HJ_Stunt_Height_Rem = 1297,
HJ_Stunt_Distance_Max_Rem = 1298,
HJ_Stunt_Distance_Max_Int = 1299,
HJ_Stunt_Rotation_Int = 1300,
HJ_Stunt_Bonus = 1302,
HJ_Stunt_Bonus_Temp = 1303,
HJ_Car_Z_Max = 1304,
HJ_Car_X = 1305,
HJ_Car_Y = 1306,
HJ_Car_Z = 1307,
HJ_Car_X_Begin_Stunt = 1308,
HJ_Car_Y_Begin_Stunt = 1309,
HJ_Car_Z_Begin_Stunt = 1310,
HJ_Car_X_End_Stunt = 1311,
HJ_Car_Y_End_Stunt = 1312,
HJ_Stunt_Distance_Max_Float = 1313,
HJ_Car_Angle_Begin_Stunt = 1314,
HJ_Car_Angle = 1315,
HJ_Stunt_Rotation_Float = 1318,
HJ_TwoWheels_Time = 1319,
HJ_TwoWheels_Distance_Meters_Int = 1320,
HJ_TwoWheels_Distance_Meters_Rem = 1321,
HJ_TwoWheels_Distance_Feet = 1322,
HJ_TwoWheels_Distance_Float = 1323,
HJ_TwoWheels_Distance_Meters_Float = 1324,
HJ_TwoWheels_Distance_Feet_Float = 1325,
HJ_Wheelie_Time = 1326,
HJ_Wheelie_Distance_Meters_Int = 1327,
HJ_Wheelie_Distance_Meters_Rem = 1328,
HJ_Wheelie_Distance_Feet = 1329,
HJ_Wheelie_Distance_Float = 1330,
HJ_Wheelie_Distance_Meters_Float = 1331,
HJ_Wheelie_Distance_Feet_Float = 1332,
HJ_Stoppie_Time = 1333,
HJ_Stoppie_Distance_Meters_Int = 1334,
HJ_Stoppie_Distance_Meters_Rem = 1335,
HJ_Stoppie_Distance_Feet = 1336,
HJ_Stoppie_Distance_Float = 1337,
HJ_Stoppie_Distance_Meters_Float = 1338,
HJ_Stoppie_Distance_Feet_Float = 1339,
HJ_Temp_Float = 1341,
-- array 70 $1342-$1411
-- 1342=Call_06_Answered
-- 1343=Call_12_Answered
-- 1344=Call_07_Answered
-- 1346=Call_18_Answered
-- 1347=Call_19_Answered
-- 1348=Call_Gym_Answered
-- 1349=Call_09_Answered
-- 1356=Call_08_Answered
-- 1357=Call_15_Answered
-- 1359=Call_24_Answered
-- 1361=Call_NUL_Answered
-- 1362=Call_10_Answered
-- 1363=Call_16_Answered
Cellphone_Call_ID = 1412,
Call_Time_Stored_Flag = 1413,
Call_Answered = 1414,
Help_SkipCall_Shown = 1418,
STAT_Progress_With_Millie = 1419,
Recall_Time = 1420,
Dialog_Index = 1421,
Dialog_Array_Size = 1422,
Dialog_WAV_Id = 1423,
-- array 20s $1424-$1442
Dialog_WAV = 1424,
-- array 20s $1445-$1483
Dialog_Subtitles = 1445,
STAT_Fat = 1485,
Mission_Vigilante_Passed = 1488,
Mission_Burglary_Passed = 1490,
Mission_Taxi_Passed = 1491,
Mission_Trucking_Passed = 1492,
Mission_Quarry_Passed = 1493,
Parachute_Creation_Stage = 1497,
FreeFall_Stage = 1513,
All_Ousters_Collected = 1516,
All_Horseshoes_Collected = 1517,
All_Photos_Taken = 1518,
All_Tags_Sprayed = 1519,
-- 1523= ROULETE attempts
-- array 32i $1524-$1555
X_Property_To_Buy = 1524,
-- array 32i $1556-$1587
Y_Property_To_Buy = 1556,
-- array 32i $1588-$1519
Z_Property_To_Buy = 1588,
-- array 32i $1653-$1684
Zero_RCShop_Bought = 1620,
-- CustomArray 32 $1621-$1652
-- 1621=Property_Markers
-- CustomArray 32 $1653-$1684
-- 1653=Property_Prices
-- array 32i $1685-$1716
Buy_Asset_Pickups = 1685,
Property_Buying_Now = 1735,
Marker_Idlewood_Barber = 1736,
Marker_Idlewood_Pizzeria = 1737,
Marker_Ganton_Clothes_Shop = 1738,
BURGLARY_Noise_f = 1792,
BURGLARY_Detected_Flag = 1793,
ONMISSION_Mike_Toreno = 1800,
Mission_Chiliad_Challenge_Passed = 1801,
Help_Chiliad_Cycling_Shown = 1805,
Mission_BeatTheCock_Passed = 1806,
Mission_BeatTheCock_Type = 1812,
-- 1817=GYM_STAT_Muscle
-- 1847=flag_mission_Wu_Zi_Mu_started
VALET_Parked_Cars_Number = 1848,
-- array 4f $1849-$1852
Shooting_X = 1849,
-- array 4f $1852-$1856
Shooting_Y = 1853,
-- array 4f $1857-$1860
Shooting_Z = 1857,
Shooting_Index = 1862,
ONMISSION_Shooting = 1863,
Shooting_Index_2 = 1864,
IMPOUND_Town_Number = 1865,
Current_Town_Number = 1866,
-- 1884= VALET control, always equals to 2 if script launched
-- 1905=SYN2_mission_attempts
-- 1909=ONMISSION_Customs_Fast_Track
flag_Player_In_Crane = 1910,
Mission_BloodRing_Passed = 1941,
-- array 10 $1942-$1951
Flight_School_Records = 1942,
Flight_School_Contests_Passed = 1952,
Flight_School_Current_Contest = 1953,
Flight_School_Brief_Played = 1954,
Pilot_License_Obtained = 1955,
parked_Rustler = 1956,
parked_Stunt = 1957,
parked_Hunter = 1958,
parked_Rustler_Exists = 1959,
parked_Stunt_Exists = 1960,
parked_Hunter_Exists = 1961,
Mission_Boat_School_Passed = 1969,
parked_Marquis_Exists = 1985,
parked_Squalo_Exists = 1986,
parked_Jetmax_Exists = 1987,
parked_Marquis = 1988,
parked_Squalo = 1989,
parked_Jetmax = 1990,
Mission_Pimping_Passed = 1991,
Mission_Courier_LS_Passed = 1992,
Mission_Courier_LV_Passed = 1993,
Mission_Courier_SF_Passed = 1994,
-------------------------------
-- $2000 - $2999
-------------------------------
parked_Freeway_Exists = 2189,
parked_FCR900_Exists = 2190,
parked_NRG500_Exists = 2191,
parked_Freeway = 2192,
parked_FCR900 = 2193,
parked_NRG500 = 2194,
BCESAR2_Times_Passed = 2195,
Mission_WuZiMu_Started_Flag = 2196,
BCESAR2_Passed_Once = 2198,
Mission_Driving_School_Passed = 2201,
Help_Respect_Shown = 2205,
Current_Tournament = 2209,
-- array 30i $2210-$2299
Races_Best_Result = 2210,
-- array 30i $2240-$2269
Races_Current_Best_Time = 2240,
-- array 30i $2270-$2299
Races_New_Best_Time = 2270,
-- array 30i $2300-$2329
Races_Won = 2300,
Races_Won_Number = 2330,
All_Races_Won_Prize_Flag = 2331,
CARMOD_Disabled_Flag = 2332,
Catalina_Dialog_Liquor_Store_Played = 2333,
Catalina_Dialog_Betting_Shop_Played = 2334,
Leviathan_Parked = 2335,
ONMISSION_GYMFIGHT = 2412,
Seller_Model = 2415,
Gyms_Accessible_Flag = 2416,
-- 2417=SHOPS_Main_Panel
-- 2418=SHOPS_Main_Panel_Exists
-- 2419=SHOPS_Shopping_Menu
-- 2420=SHOPS_Shopping_Menu_Exists
-- 2421=SHOPS_Panel_Bought
-- 2422=SHOPS_Panel_Bought_Exists
-- 2423=SHOPS_Shopping_Submenu
-- 2424=SHOPS_Shopping_Submenu_Exists
-- array 12s $2450-$2472
-- 2450=SHOPS_Item_Names
ShoppingItem_ModelCRC = 2510,
ShoppingItem_BodyPart = 2511,
-- girlfriend gifts
Gimp_Suit_Available = 2555,
Valet_Uniform_Available = 2556,
Croupier_Uniform_Available = 2557,
Cop_Uniform_Available = 2558,
Rural_Clothes_Available = 2559,
Racing_Suit_Available = 2560,
Medic_Uniform_Available = 2561,
Pimp_Suit_Available = 2562,
-- 2564=flag_weapons_6
-- 2565=flag_weapons_7
-- 2566=flag_weapons_8
-- 2567=flag_weapons_9
-- 2568=flag_weapons_10
-- 2569=flag_weapons_11
-- 2570=flag_weapons_12
-- 2571=flag_weapons_13
-- 2572=flag_weapons_14
-- 2573=flag_weapons_15
-- 2574=flag_weapons_all
SFGym_Icon = 2630,
LVGym_Icon = 2631,
-- 2657=Total_Unique_Jumps = useless, used once
Object_M_A51_BLASTDOORR = 2667,
Object_M_A51_BLASTDOORL = 2668,
Crane_Magnet = 2729,
-- 2733=Magno_Base_X
-- 2734=Magno_Base_Y
-- 2736=Magno_Cabin_H
-- 2737=Magno_Arm_H
-- 2738=Magno_Arm_Rotate_Y
flag_Player_Attack_Food_Seller = 2748,
Shops_Player_Cash = 2754,
parked_Rhino = 2777,
Mission_BMX_Stunt_Passed = 2795,
Mission_NRG500_Stunt_Passed = 2796,
-- array 2i $2797-$2798
STUNT_Missions_BestTime = 2797,
parked_BMX_Glen_Park = 2799,
X_STUNT_Mission_BMX = 2800,
Y_STUNT_Mission_BMX = 2801,
Z_STUNT_Mission_BMX = 2802,
parked_NRG500_Easter_Basin = 2803,
X_STUNT_Mission_NRG500 = 2804,
Y_STUNT_Mission_NRG500 = 2805,
Z_STUNT_Mission_NRG500 = 2806,
-- array 6 $2818-$2823
-- michelle= helena= barbara= katie= millie
parked_Girlfriend_Cars = 2818,
-- array 6 $2824-$2829
parked_Girlfriend_Cars_numplate = 2824,
-- array 30 $2830-$2859
parked_IMPEXPM_cars = 2830,
-------------------------------
-- $3000 - $3999
-------------------------------
Color_Red = 3397,
Color_Green = 3398,
-- 3399=Color_Blue
Gambles_CashWin = 3399,
-- array 85f $3929-$4013
-- array 151f $3507-$3657
-- 3507= unknown gambles multiplier
X_BCE2_Checkpoints = 3929,
-------------------------------
-- $4000 - $4999
-------------------------------
--array 85f $4014-$4098
Y_BCE2_Checkpoints = 4014,
--array 85f $4099-$4183
Z_BCE2_Checkpoints = 4099,
-- array 116f $4184-$4299
X_BCE2_Patriot_Checkpoints = 4184,
-- array 116f $4300-$4415
Y_BCE2_Patriot_Checkpoints = 4300,
-- array 116f $4416-$4531
Z_BCE2_Patriot_Checkpoints = 4416,
-------------------------------
-- $5000 - $5999
-------------------------------
Object_M_A51_VENTCOVERB = 5177,
-- array 28 $5189-$5216
parked_Planes = 5189,
-- array 12 $5117-$5228
parked_SF_planes = 5217,
BURGLARY_Noise_i = 5293,
DANCE_SCORE = 5296,
DANCE_ANIM = 5299,
DANCE_ANIM_IFP = 5307,
Current_Month_Day = 5345,
Current_Month = 5346,
GYM_Month_Day_When_Limit_Reached = 5347,
GYM_Month_When_Limit_Reached = 5348,
GYM_Day_Limit = 5349,
-- array 37s $5993- $6068
SHTR_characters = 5993,
-------------------------------
-- $6000 - $6999
-------------------------------
BeefyBaron_Time = 6473,
BeefyBaron_Score = 6474,
CPRACE_Debug_Checkpoint_Index = 6959,
CAT1_Pickups_Collected = 6987,
-------------------------------
-- $7000 - $7999
-------------------------------
STAT_Lung_Capacity_for_wuzi = 7263,
SYN2_Time_Until_Backup = 7300,
-- 7301=area_name ?
ZERO4_Time = 7432,
ZERO4_Car_Health = 7433,
-------------------------------
-- $8000 - $8999
-------------------------------
Trucking_Total_Passed_Missions = 8159,
Quarry_Missions_Passed = 8171,
-- array 7 $8174-$8180
-- 8174=rockfall
-- array 8 $8181-$8188
Quarry_Stage = 8181,
-- 8201=useless= tempvar, keeps $56 value
Paramedic_Mission_Level = 8211,
BUGRLARY_Cash = 8228,
-- array 3s $8229-$8233
Burglary_Garages = 8229,
BURGLARY_Daylight = 8235,
BURGLARY_Escape_Time_Sec = 8236,
BURGLARY_Time_left = 8237,
Pickup_Parachute = 8269,
BCE_Checkpoints_Index = 8395,
ROULETE_Cash_Won = 8550,
ROULETE_Player_Cash = 8559,
STAT_Gambling_Skill = 8560,
-- array 151h $8561-$8711
ROULETE_Chips = 8561,
GYM_GymBike_Power = 8717,
GYM_GymBike_Level = 8718,
GYM_GymBike_Distance = 8719,
GYM_Bench_Weight = 8739,
GYM_STAT_Bench_Heaviest_Weight = 8740,
-- 8747=GYM_STAT_Muscle_2
GYM_Dumbbells_Panel = 8759,
GYM_Dumbbells_Panel_Selected_Index = 8764,
GYM_STAT_Dumbbells_Heaviest_Weight = 8770,
GYM_Dumbbells_Weight = 8771,
-- 8822=Z_offset_from_BBHOOP
-- 8823=X_offset_from_BBHOOP
-- 8824=Y_offset_from_BBHOOP
High_Cards_Number = 8868,
-- array 5 $8873-$8877
POKER_X_Card = 8873,
-- array 5 $8878-$8892
POKER_Y_Card = 8878,
POKER_Y_Row = 8893,
POKER_X_Bet_One = 8883,
POKER_Y_Bet_One = 8884,
POKER_X_Deal = 8885,
POKER_Y_Deal = 8886,
POKER_X_Col = 8887,
POKER_Counter = 8907,
POKER_X_screen = 8908,
POKER_Y_screen = 8916,
POKER_Card_Height = 8924,
POKER_Card_Width = 8925,
POKER_Hold_Button_Offset = 8926,
POKER_Hold_Width = 8927,
POKER_Hold_Height = 8928,
POKER_Border_Thickness = 8929,
POKER_Deal_Width = 8932,
POKER_Deal_Height = 8933,
POKER_Bet_One_Width = 8934,
POKER_Bet_One_Height = 8935,
POKER_X_Line = 8936,
POKER_Y_Line = 8945,
POKER_Line_Width = 8954,
POKER_Line_Height = 8963,
POKER_X_Text_Scale = 8972,
POKER_Y_Text_Scale = 8978,
POKER_Text_Color_R = 8984,
POKER_Text_Color_G = 8990,
POKER_Text_Color_B = 8996,
-------------------------------
-- $9000 - $9999
-------------------------------
POKER_Text_Font = 9002,
POKER_Text_Centre = 9008,
POKER_Text_Edge = 9014,
POKER_Text_Edge_R = 9020,
POKER_Text_Edge_G = 9026,
POKER_Text_Edge_B = 9032,
POKER_Button_Text_Offset = 9038,
POKER_Tile_Type = 9039,
POKER_Table_X = 9040,
POKER_Table_Y = 9041,
POKER_Table_Width = 9042,
POKER_Table_Height = 9043,
-- 9044=POKER_Highl_X
-- 9049=POKER_Highl_Y
-- 9054=POKER_Highl_Width
-- 9059=POKER_Highl_Height
POKER_Tile_Width = 9067,
POKER_Tile_Height = 9068,
BJACK_X_Player_Sprite = 9071,
BJACK_Y_Player_Sprite = 9079,
-- 9087=player_sprite1_X
-- 9095=player_sprite1_Y
-- 9103=player_sprite1B_X
-- 9111=player_sprite1B_Y
-- 9119=player_sprite2_X
-- 9127=player_sprite2_Y
BJACK_Card_Width = 9135,
BJACK_Card_Height = 9136,
BJACK_Border_Width = 9138,
BJACK_Card_ID = 9140,
BJACK_Player_cannot_Split = 9165,
BJACK_Player_has_split = 9166,
BJACK_Player_cannot_double = 9167,
BJACK_Player_has_doubled1 = 9168,
BJACK_Player_has_doubled2 = 9169,
BJACK_Player_has_stucked1 = 9170,
BJACK_Player_has_stucked2 = 9171,
BJACK_Player_has_hit1 = 9172,
BJACK_Dealer_has_stuck = 9173,
BJACK_Player_has_quit = 9174,
BJACK_Player_has_won1 = 9175,
BJACK_Player_has_won2 = 9176,
BJACK_Player_has_input = 9177,
BJACK_Dealer_wins_by_default = 9178,
BJACK_Player_total1 = 9179,
BJACK_Player_total1_B = 9180,
BJACK_Player_total2 = 9181,
BJACK_Player_total2_B = 9182,
BJACK_Dealer_total = 9183,
BJACK_Dealer_total2 = 9184,
BJACK_Dealer_got_ace = 9185,
BJACK_Player_got_ace1 = 9186,
BJACK_Player_got_ace2 = 9187,
BJACK_Player_got_BJACK = 9188,
BJACK_Player_cash = 9190,
BJACK_last_bet = 9191,
BJACK_Bet1 = 9192,
BJACK_Bet2 = 9193,
BJACK_Bet_Step = 9194,
BJACK_Half_Bet = 9196,
BJACK_Refund = 9197,
BJACK_Initial_Stake = 9198,
BJACK_Payout = 9199,
Max_Wager = 9200,
BJACK_Cheat_Card_ID = 9228,
BJACK_X_Screenpos = 9229,
BJACK_Y_Screenpos = 9256,
BJACK_X_Chip_offset = 9283,
BJACK_Y_Chip_offset = 9291,
BJACK_Z_Chip_offset = 9299,
BJACK_Flag_Card_Cheat = 9310,
-- 9321=BJACK_Z_table_offset
-- 9322=BJACK_X_table_offset
-- 9323=BJACK_Y_table_offset
-- 9446=BJACK_Z_table2_offset
-- 9447=BJACK_X_table_offset
-- 9448=BJACK_Y_table_offset
-- array 7s $9480-$9495
PLANES_DayNames = 9480,
PLANES_Weekday = 9496,
PLANES_STAT_Unlocked_Cities_Number = 9497,
PLANES_Conversation_YES = 9498,
PLAINS_Plane_Flyway = 9507,
TRAINS_Text_Shown = 9523,
TRAINS_Train_Handle = 9524,
TRAINS_Train_Speed = 9525,
AMMU_Seller = 9540,
AMMU_Weapon_Cost = 9541,
AMMU_Available_Weapons = 9542,
AMMU_Weapon_Name = 9545,
AMMU_Interior_Name = 9547,
AMMU_Selected_Weapon_Weapon_Group = 9551,
AMMU_Selected_Weapon = 9552,
AMMU_Selected_Weapon_Model = 9553,
AMMU_Seller_Animation = 9562,
AMMU_Stage = 9579,
flag_AMMU_Seller_Attack_Player = 9580,
AMMU_Seller_Animation_Time = 9581,
AMMU_Seller_Angle = 9582,
AMMU_Player_Angle = 9583,
AMMU_X_Offset = 9588,
AMMU_Y_Offset = 9589,
AMMU_Z_Offset = 9590,
AMMU_X_Aim_Camera = 9591,
AMMU_Y_Aim_Camera = 9592,
AMMU_Z_Aim_Camera = 9593,
TATOO_Selected_Torso_Part = 9710,
-- 9734=selected_haircut
-------------------------------
-- $10000 - $10947
-------------------------------
Food_Seller = 10032,
Food_Seller_Exists = 10033,
JFUD_Selected_Food = 10037,
JFUD_Stage = 10046,
JFUD_STAT_Fat = 10053,
JFUD_Player_Health = 10054,
JFUD_X_Food_Seller = 10057,
JFUD_Y_Food_Seller = 10058,
JFUD_Z_Food_Seller = 10059,
JFUD_Food_Seller_Angle = 10060,
JFUD_X_Offset = 10061,
JFUD_Y_Offset = 10062,
JFUD_Z_Offset = 10063,
JFUD_X_Camera = 10065,
JFUD_Y_Camera = 10066,
JFUD_Z_Camera = 10067,
JFUD_X_Aim_Camera = 10068,
JFUD_Y_Aim_Camera = 10069,
JFUD_Z_Aim_Camera = 10070,
JFUD_LOW_Food_Model = 10071,
JFUD_MED_Food_Model = 10072,
JFUD_HIGH_Food_Model = 10073,
JFUD_HEALTHY_Food_Model = 10074,
CARMOD_Car = 10431,
CARMOD_Menu_Colors = 10426,
Crane_Rope_Length = 10496,
VALET_Car = 10751,
VALET_Car_Driver = 10746,
VALET_Stage = 10941
-- 10947=useless -- highest variable
}
