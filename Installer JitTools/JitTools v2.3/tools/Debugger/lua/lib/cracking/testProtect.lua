script_author("<PERSON><PERSON><PERSON>llis<PERSON>")
script_version("133.7")

local funcTypes = {
	['renderGetFontCharIndexAt'] = "C",
	['sampGetChatString'] = "C",
	['isCarDoorFullyOpen'] = "C",
	['removeSphere'] = "C",
	['decrementFloatStat'] = "C",
	['popCarPanel'] = "C",
	['planeGotoCoords'] = "C",
	['rawget'] = "C",
	['startNewStreamedScript'] = "C",
	['locateObject3d'] = "C",
	['hasMissionAudioFinished'] = "C",
	['locateCharInCar3d'] = "C",
	['taskStandStill'] = "C",
	['loadDynamicLibrary'] = "C",
	['getNumberOfFollowers'] = "C",
	['setCharStayInSamePlace'] = "C",
	['getPlayerMoney'] = "C",
	['addBlipForObject'] = "C",
	['sampGetCurrentDialogListItem'] = "C",
	['removeBlip'] = "C",
	['getScriptTaskStatus'] = "C",
	['printWithNumberBig'] = "C",
	['createOysterPickup'] = "C",
	['removePickup'] = "C",
	['getNameOfInfoZone'] = "C",
	['taskSwimToCoord'] = "C",
	['hasPlayerBoughtItem'] = "C",
	['taskPlayAnimSecondary'] = "C",
	['explodeCharHead'] = "C",
	['hasSaveGameFinished'] = "C",
	['getCharCoordinates'] = "C",
	['loadAndLaunchMissionInternal'] = "C",
	['getHudColour'] = "C",
	['isCarStuck'] = "C",
	['isObjectInArea3d'] = "C",
	['getTickCount'] = "C",
	['createPickup'] = "C",
	['setPlayerGroupToFollowNever'] = "C",
	['getOffsetFromCharInWorldCoords'] = "C",
	['taskSetIgnoreWeaponRangeFlag'] = "C",
	['isCharDead'] = "C",
	['attachAnimsToModel'] = "C",
	['stopCharFacialTalk'] = "C",
	['playerEnteredLasVegasCrane'] = "C",
	['isDebugCameraOn'] = "C",
	['getObjectModel'] = "C",
	['setCharSayContext'] = "C",
	['sampSendClickPlayer'] = "C",
	['status'] = "C", -- table: jit
	['on'] = "C", -- table: jit
	['off'] = "C", -- table: jit
	['flush'] = "C", -- table: jit
	['attach'] = "C", -- table: jit
	['isSampfuncsLoaded'] = "C",
	['getCarNumberOfGears'] = "C",
	['sampTextdrawGetOutlineColor'] = "C",
	['locateCharInCarCar2d'] = "C",
	['createChar'] = "C",
	['deleteMissionTrains'] = "C",
	['hasObjectCollidedWithAnything'] = "C",
	['loadGroupDecisionMaker'] = "C",
	['sampIsChatInputActive'] = "C",
	['getModuleHandle'] = "C",
	['displayNthOnscreenCounterWithString'] = "C",
	['sampGetVehicleIdByCarHandle'] = "C",
	['setTextProportional'] = "C",
	['addToCarRotationVelocity'] = "C",
	['displayOnscreenTimerWithString'] = "C",
	['taskHandGesture'] = "C",
	['shutCharUp'] = "C",
	['isCharInAngledArea2d'] = "C",
	['registerMissionGiven'] = "C",
	['closeGarage'] = "C",
	['getScreenResolution'] = "C",
	['getCharPointer'] = "C",
	['setBlipEntryExit'] = "C",
	['isConversationAtNode'] = "C",
	['printWithNumber'] = "C",
	['getClothesItem'] = "C",
	['clearLookAt'] = "C",
	['limitTwoPlayerDistance'] = "C",
	['switchRoadsOff'] = "C",
	['setBlipAlwaysDisplayOnZoomedRadar'] = "C",
	['tonumber'] = "C",
	['setCameraBehindPlayer'] = "C",
	['getRandomCharInSphereOnlyDrugsBuyers'] = "C",
	['drawOddjobTitleBeforeFade'] = "C",
	['findClose'] = "C",
	['getNthClosestCarNode'] = "C",
	['createScmThreadAtPointer'] = "C",
	['placeWaypoint'] = "C",
	['stopPlaybackRecordedCar'] = "C",
	['getCarSpeedVector'] = "C",
	['isPlayerWearing'] = "C",
	['decodeJson'] = "C",
	['attachCarToObject'] = "C",
	['detachCharFromCar'] = "C",
	['convertMatrixToQuaternion'] = "C",
	['hasVehicleModLoaded'] = "C",
	['switchWorldProcessing'] = "C",
	['taskCharArrestChar'] = "C",
	['setCharRelationship'] = "C",
	['isCleoLoaded'] = "C",
	['isPlayerInInfoZone'] = "C",
	['doCameraBump'] = "C",
	['setPlayerControl'] = "C",
	['setPoliceIgnorePlayer'] = "C",
	['dxutGetListboxItemTextAndData'] = "C",
	['script_dependencies'] = "C",
	['addBigGunFlash'] = "C",
	['unmarkAllRoadNodesAsDontWander'] = "C",
	['setupvalue'] = "C", -- table: debug
	['getregistry'] = "C", -- table: debug
	['traceback'] = "C", -- table: debug
	['setlocal'] = "C", -- table: debug
	['getupvalue'] = "C", -- table: debug
	['gethook'] = "C", -- table: debug
	['sethook'] = "C", -- table: debug
	['getlocal'] = "C", -- table: debug
	['upvaluejoin'] = "C", -- table: debug
	['getinfo'] = "C", -- table: debug
	['getfenv'] = "C", -- table: debug
	['setmetatable'] = "C", -- table: debug
	['upvalueid'] = "C", -- table: debug
	['getuservalue'] = "C", -- table: debug
	['debug'] = "C", -- table: debug
	['getmetatable'] = "C", -- table: debug
	['setfenv'] = "C", -- table: debug
	['setuservalue'] = "C", -- table: debug
	['setNearClip'] = "C",
	['taskHandsUp'] = "C",
	['setCharQuaternion'] = "C",
	['locateStoppedCharInCar3d'] = "C",
	['taskChatWithChar'] = "C",
	['attachObjectToChar'] = "C",
	['clearRelationship'] = "C",
	['registerScriptBrainForCodeUse'] = "C",
	['givePlayerMoney'] = "C",
	['dxutEnableDialogCaption'] = "C",
	['main'] = "L",
	['raknetEmulRpcReceiveBitStream'] = "C",
	['createCharAsPassenger'] = "C",
	['disableCrashWaiting'] = "L",
	['huy'] = "L",
	['loltestkek'] = "L",
	['raknetGetPacketName'] = "C",
	['clearHeliOrientation'] = "C",
	['deleteObject'] = "C",
	['resetStuffUponResurrection'] = "C",
	['taskShuffleToNextCarSeat'] = "C",
	['setfenv'] = "C",
	['dxutCreateDialog'] = "C",
	['getCarDoorLockStatus'] = "C",
	['getPedType'] = "C",
	['setObjectTurnMass'] = "C",
	['setHeliStabiliser'] = "C",
	['setEveryoneIgnorePlayer'] = "C",
	['sampfuncsRegisterConsoleCommand'] = "C",
	['displayCarNames'] = "C",
	['dxutSetControlText'] = "C",
	['addSmokeParticle'] = "C",
	['sampGet3dTextInfoById'] = "C",
	['raknetBitStreamEncodeString'] = "C",
	['raknetBitStreamDecodeString'] = "C",
	['setInformRespectedFriends'] = "C",
	['createMoneyPickup'] = "C",
	['getFadingStatus'] = "C",
	['setAlwaysDraw3dMarkers'] = "C",
	['setPlayerDuckButton'] = "C",
	['removeGroup'] = "C",
	['setIntStat'] = "C",
	['cameraResetNewScriptables'] = "C",
	['raknetBitStreamReadBuffer'] = "C",
	['taskWarpCharIntoCarAsPassenger'] = "C",
	['raknetBitStreamReadFloat'] = "C",
	['convertMetresToFeetInt'] = "C",
	['destroyScmThread'] = "C",
	['raknetBitStreamReadInt32'] = "C",
	['allowFixedCameraCollision'] = "C",
	['raknetBitStreamReadInt8'] = "C",
	['raknetNewBitStream'] = "C",
	['setCharSayContextImportant'] = "C",
	['raknetBitStreamReadBool'] = "C",
	['setPlaybackSpeed'] = "C",
	['sampIsDialogActive'] = "C",
	['createObjectNoOffset'] = "C",
	['setPlayerDisplayVitalStatsButton'] = "C",
	['taskGreetPartner'] = "C",
	['raknetBitStreamWriteBuffer'] = "C",
	['removeTextureDictionary'] = "C",
	['getCarUprightValue'] = "C",
	['raknetBitStreamWriteFloat'] = "C",
	['raknetBitStreamWriteInt32'] = "C",
	['raknetBitStreamWriteInt16'] = "C",
	['setCarEscortCarLeft'] = "C",
	['setCarDrivingStyle'] = "C",
	['getFreeGxtKey'] = "C",
	['addAmmoToChar'] = "C",
	['addPriceModifier'] = "C",
	['raknetBitStreamWriteInt8'] = "C",
	['doesBlipExist'] = "C",
	['raknetBitStreamIgnoreBits'] = "C",
	['raknetBitStreamWriteBool'] = "C",
	['setObjectCollision'] = "C",
	['sampGetAnimationNameAndFile'] = "C",
	['sampUnregisterChatCommand'] = "C",
	['markModelAsNoLongerNeeded'] = "C",
	['raknetBitStreamWriteString'] = "C",
	['taskPause'] = "C",
	['setCinemaCamera'] = "C",
	['taskWeaponRoll'] = "C",
	['printBigQ'] = "C",
	['playerMadeProgress'] = "C",
	['canCharSeeDeadChar'] = "C",
	['setTrainSpeed'] = "C",
	['sampGetPlayerNickname'] = "C",
	['setScriptLimitToGangSize'] = "C",
	['setObjectDrawLast'] = "C",
	['sampGetCarHandleBySampVehicleId'] = "C",
	['getNameOfItem'] = "C",
	['sampAddChatMessage'] = "C",
	['getCleoLibraryVersion'] = "C",
	['isSampfuncsConsoleCommandDefined'] = "C",
	['setPlayerFireButton'] = "C",
	['rol'] = "C", -- table: bit
	['rshift'] = "C", -- table: bit
	['ror'] = "C", -- table: bit
	['bswap'] = "C", -- table: bit
	['bxor'] = "C", -- table: bit
	['bor'] = "C", -- table: bit
	['arshift'] = "C", -- table: bit
	['bnot'] = "C", -- table: bit
	['tobit'] = "C", -- table: bit
	['lshift'] = "C", -- table: bit
	['tohex'] = "C", -- table: bit
	['band'] = "C", -- table: bit
	['addBlipForCarOld'] = "C",
	['isPs2KeyboardKeyPressed'] = "C",
	['getNumberOfFiresInArea'] = "C",
	['hasStreamedScriptLoaded'] = "C",
	['isCharInAnyHeli'] = "C",
	['sampGetPlayerCount'] = "C",
	['clearLastBuildingModelShotByPlayer'] = "C",
	['sampForceWeaponsSync'] = "C",
	['startKillFrenzy'] = "C",
	['loadAllModelsNow'] = "C",
	['openGarage'] = "C",
	['startScriptFire'] = "C",
	['sampForceStatsSync'] = "C",
	['getPositionOfEntryExitCharUsed'] = "C",
	['dxutGetControlSize'] = "C",
	['setCarCruiseSpeed'] = "C",
	['sampForceTrailerSync'] = "C",
	['sampForceAimSync'] = "C",
	['stopBeatTrack'] = "C",
	['getWheelieStats'] = "C",
	['type'] = "C",
	['sampGetListboxItemText'] = "C",
	['isCharInSearchlight'] = "C",
	['sampForceOnfootSync'] = "C",
	['skipToNextAllowedStation'] = "C",
	['sampForceUnoccupiedSyncSeatId'] = "C",
	['sampForceVehicleSync'] = "C",
	['setTextFont'] = "C",
	['setZoneGangStrength'] = "C",
	['isAreaOccupied'] = "C",
	['removeCarRecording'] = "C",
	['setMinigameInProgress'] = "C",
	['createFxSystemOnObjectWithDirection'] = "C",
	['getCollectable1sCollected'] = "C",
	['sampGetPlayerIdByCharHandle'] = "C",
	['customPlateForNextCar'] = "C",
	['setTextRightJustify'] = "C",
	['getScmThreadLocalVar'] = "C",
	['setProgressTotal'] = "C",
	['setScmThreadLocalVar'] = "C",
	['createSearchlight'] = "C",
	['runSampfuncsConsoleCommand'] = "C",
	['isProceduralInteriorActive'] = "C",
	['hasCharSpottedChar'] = "C",
	['isSampfuncsGlobalVarDefined'] = "C",
	['addOneOffSound'] = "C",
	['clearCharTasksImmediately'] = "C",
	['raknetBitStreamGetNumberOfBytesUsed'] = "C",
	['vehicleDoesProvideCover'] = "C",
	['sampTextdrawGetLetterSizeAndColor'] = "C",
	['sampTextdrawGetPos'] = "C",
	['readMemory'] = "C",
	['sampTextdrawGetString'] = "C",
	['skipInPlaybackRecordedCar'] = "C",
	['setObjectAnimSpeed'] = "C",
	['import'] = "C",
	['printHelpForeverWithNumber'] = "C",
	['clearCharLastWeaponDamage'] = "C",
	['slideObject'] = "C",
	['getNumberOfFiresInRange'] = "C",
	['sampTextdrawGetShadowColor'] = "C",
	['preloadBeatTrack'] = "C",
	['setVehicleAirResistanceMultiplier'] = "C",
	['isSkipCutsceneButtonPressed'] = "C",
	['sampTextdrawGetStyle'] = "C",
	['getCharWeaponInSlot'] = "C",
	['setCarWatertight'] = "C",
	['resetHavocCausedByPlayer'] = "C",
	['copyGroupDecisionMaker'] = "C",
	['setTankDetonateCars'] = "C",
	['skipToEndAndStopPlaybackRecordedCar'] = "C",
	['markMissionTrainsAsNoLongerNeeded'] = "C",
	['sampTextdrawGetAlign'] = "C",
	['sampTextdrawGetBoxEnabledColorAndSize'] = "C",
	['cameraIsVectorTrackRunning'] = "C",
	['switchAmbientPlanes'] = "C",
	['allocateMemory'] = "C",
	['setSpecificZoneToTriggerGangWar'] = "C",
	['sampProcessChatInput'] = "C",
	['isThisModelABoat'] = "C",
	['sampSendDialogResponse'] = "C",
	['encodeJson'] = "C",
	['sampTextdrawSetString'] = "C",
	['sampTextdrawSetModelRotationZoomVehColor'] = "C",
	['unpack'] = "C",
	['addVehicleMod'] = "C",
	['sampRequestClass'] = "C",
	['setCarOnlyDamagedByPlayer'] = "C",
	['taskShootAtChar'] = "C",
	['sampTextdrawSetShadow'] = "C",
	['sampGetCurrentServerName'] = "C",
	['taskLookAbout'] = "C",
	['isThisHelpMessageBeingDisplayed'] = "C",
	['setCharAllowedToDuck'] = "C",
	['setTextDropshadow'] = "C",
	['sampTextdrawSetStyle'] = "C",
	['isCarModel'] = "C",
	['setNextDesiredMoveState'] = "C",
	['sampTextdrawSetProportional'] = "C",
	['setStructFloatElement'] = "C",
	['getClosestCarNode'] = "C",
	['setTextJustify'] = "C",
	['sampTextdrawSetBoxColorAndSize'] = "C",
	['isCharInAnyBoat'] = "C",
	['setLoadCollisionForCarFlag'] = "C",
	['getCharHighestPriorityEvent'] = "C",
	['setTwoPlayerCameraMode'] = "C",
	['taskDrivePointRoute'] = "C",
	['setObjectProofs'] = "C",
	['sampGetPlayerScore'] = "C",
	['sampSet3dTextString'] = "C",
	['declareMissionFlag'] = "C",
	['stopFxSystem'] = "C",
	['isGarageClosed'] = "C",
	['isMinigameInProgress'] = "C",
	['setGxtEntry'] = "C",
	['setCharDropsWeaponsWhenDead'] = "C",
	['doesObjectExist'] = "C",
	['registerAttractorScriptBrainForCodeUse'] = "C",
	['setSampfuncsGlobalVar'] = "C",
	['taskDiveFromAttachmentAndGetUp'] = "C",
	['disableCharSpeech'] = "C",
	['getRopeHeightForObject'] = "C",
	['raknetBitStreamGetDataPtr'] = "C",
	['setTimeScale'] = "C",
	['locateStoppedCharAnyMeans2d'] = "C",
	['raknetBitStreamGetWriteOffset'] = "C",
	['setCharIsTargetPriority'] = "C",
	['pointSearchlightAtCoord'] = "C",
	['raknetBitStreamGetNumberOfUnreadBits'] = "C",
	['sampTextdrawDelete'] = "C",
	['rawequal'] = "C",
	['raknetBitStreamGetNumberOfBitsUsed'] = "C",
	['doesSearchlightExist'] = "C",
	['taskShakeFist'] = "C",
	['raknetBitStreamSetReadOffset'] = "C",
	['taskDiveAndGetUp'] = "C",
	['freeTextures'] = "C",
	['markCarAsNoLongerNeeded'] = "C",
	['raknetBitStreamResetWritePointer'] = "C",
	['sampGetSampPoolsPtr'] = "C",
	['next'] = "C",
	['setNamedEntryExitFlag'] = "C",
	['setCharAccuracy'] = "C",
	['createMenuGrid'] = "C",
	['resumeScmThread'] = "C",
	['pauseScmThread'] = "C",
	['sampSetChatDisplayMode'] = "C",
	['registerIntStat'] = "C",
	['setRopeHeightForObject'] = "C",
	['sampGetChatDisplayMode'] = "C",
	['setCharNeverTargetted'] = "C",
	['markVehicleModAsNoLongerNeeded'] = "C",
	['isCharTouchingObject'] = "C",
	['restoreCamera'] = "C",
	['isPlaybackGoingOnForCar'] = "C",
	['attachSearchlightToSearchlightObject'] = "C",
	['dontSuppressAnyCarModels'] = "C",
	['setTagStatusInArea'] = "C",
	['sampIsDialogClientside'] = "C",
	['sampSetDialogClientside'] = "C",
	['dumpScmThread'] = "C",
	['addPedtypeAsAttractorUser'] = "C",
	['sampGetDialogCaption'] = "C",
	['switchEmergencyServices'] = "C",
	['sampGetDialogText'] = "C",
	['sampToggleScoreboard'] = "C",
	['sampIsScoreboardOpen'] = "C",
	['sampSendVehicleDestroyed'] = "C",
	['sampSendMenuQuit'] = "C",
	['sampSendMenuSelectRow'] = "C",
	['taskStayInSamePlace'] = "C",
	['setHeadingForAttachedPlayer'] = "C",
	['locateCharOnFoot3d'] = "C",
	['switchRoadsBackToOriginal'] = "C",
	['isCarDoorDamaged'] = "C",
	['raknetBitStreamReadInt16'] = "C",
	['setGroupDefaultTaskAllocator'] = "C",
	['setCarProofs'] = "C",
	['getModuleProcAddress'] = "C",
	['sampSendRequestSpawn'] = "C",
	['registerBestPosition'] = "C",
	['sampSetSpecialAction'] = "C",
	['raiseLuaError'] = "C",
	['taskGotoCharAiming'] = "C",
	['getControllerMode'] = "C",
	['pairs'] = "C",
	['dxutSetSliderValue'] = "C",
	['setGroupLeader'] = "C",
	['getAllVehicles'] = "C",
	['setCarVisible'] = "C",
	['sampSendEditAttachedObject'] = "C",
	['setFadingColour'] = "C",
	['sampSendEditObject'] = "C",
	['isPlayerControlOn'] = "C",
	['sampSendTakeDamage'] = "C",
	['sampSendGiveDamage'] = "C",
	['disableHeliAudio'] = "C",
	['sampSendClickTextdraw'] = "C",
	['isPlayerPlaying'] = "C",
	['getMaximumNumberOfPassengers'] = "C",
	['sampSendSpectatorData'] = "C",
	['sampSendUnoccupiedData'] = "C",
	['dumpScmCallbackSystem'] = "C",
	['isVehicleAttached'] = "C",
	['incrementIntStat'] = "C",
	['getMenuItemSelected'] = "C",
	['playMissionPassedTune'] = "C",
	['sampSendTrailerData'] = "C",
	['addEventHandler'] = "C",
	['sampSendBulletData'] = "C",
	['displayNonMinigameHelpMessages'] = "C",
	['isSampAvailable'] = "C",
	['removeIplDiscreetly'] = "C",
	['locateCharInCar2d'] = "C",
	['getObjectHeading'] = "C",
	['taskTired'] = "C",
	['collectgarbage'] = "C",
	['sampSendPassengerData'] = "C",
	['setObjectHeading'] = "C",
	['sampSendIncarData'] = "C",
	['setInteriorVisible'] = "C",
	['swapNearestBuildingModel'] = "C",
	['dxutDeleteDialog'] = "C",
	['isCarWaitingForWorldCollision'] = "C",
	['isCharShooting'] = "C",
	['createFxSystem'] = "C",
	['shutPlayerUp'] = "C",
	['isCharStoppedInAngledArea2d'] = "C",
	['isCharAtScriptedAttractor'] = "C",
	['sampStorePlayerAimData'] = "C",
	['sampStorePlayerTrailerData'] = "C",
	['setAllTaxisHaveNitro'] = "C",
	['unpausePlaybackRecordedCar'] = "C",
	['dxutCheckboxSetChecked'] = "C",
	['print'] = "C",
	['addStuckCarCheck'] = "C",
	['sampStorePlayerOnfootData'] = "C",
	['markObjectAsNoLongerNeeded'] = "C",
	['isCarLowRider'] = "C",
	['isKeyJustPressed'] = "C",
	['taskUseAtm'] = "C",
	['sampGetPickupPoolPtr'] = "C",
	['sampGetVehiclePoolPtr'] = "C",
	['fetchNextCard'] = "C",
	['switchPedRoadsOn'] = "C",
	['attachCharToObject'] = "C",
	['sampGetPlayerPoolPtr'] = "C",
	['sampGetTextlabelPoolPtr'] = "C",
	['createCharInsideCar'] = "C",
	['getGroundZFor3dCoord'] = "C",
	['isCharSittingInCar'] = "C",
	['switchArrestPenalties'] = "C",
	['sampGetGangzonePoolPtr'] = "C",
	['getRandomCharInAreaOffsetNoSave'] = "C",
	['setClipboardText'] = "C",
	['isCharModel'] = "C",
	['pointSearchlightAtVehicle'] = "C",
	['sampGetObjectPoolPtr'] = "C",
	['isCharAttachedToAnyCar'] = "C",
	['isCharStoppedInAngledAreaOnFoot2d'] = "C",
	['taskDieNamedAnim'] = "C",
	['streamScript'] = "C",
	['getCarMovingComponentOffset'] = "C",
	['sampGetKillInfoPtr'] = "C",
	['loadSprite'] = "C",
	['reloadScripts'] = "C",
	['setFreeHealthCare'] = "C",
	['forceWeatherNow'] = "C",
	['getUserOfClosestMapAttractor'] = "C",
	['sampGetChatInfoPtr'] = "C",
	['sampGetInputInfoPtr'] = "C",
	['clearCharDecisionMakerEventResponse'] = "C",
	['ensurePlayerHasDriveByWeapon'] = "C",
	['sampGetSampInfoPtr'] = "C",
	['getClipboardText'] = "C",
	['sampGetRpcNodeByRpcId'] = "C",
	['isCharShootingInArea'] = "C",
	['dxutAddCheckbox'] = "C",
	['winchCanPickVehicleUp'] = "C",
	['sampGetRakclientFuncAddressByIndex'] = "C",
	['sampGetRakpeer'] = "C",
	['requestCollision'] = "C",
	['sampGetRakclientInterface'] = "C",
	['enableConversation'] = "C",
	['getSampfuncsGlobalVarAccessForThread'] = "C",
	['giveVehiclePaintjob'] = "C",
	['getNumberOfItemsInShop'] = "C",
	['setPhotoCameraEffect'] = "C",
	['setCarHeavy'] = "C",
	['startCutscene'] = "C",
	['dxutGetControlPos'] = "C",
	['dxutSetCheckboxColor'] = "C",
	['dxutSetControlPos'] = "C",
	['setSequenceToRepeat'] = "C",
	['dxutSetControlSize'] = "C",
	['dxutSetFocusOnControl'] = "C",
	['sampSendRconCommand'] = "C",
	['dxutDeleteControl'] = "C",
	['releaseTwoPlayerDistance'] = "C",
	['printWith4Numbers'] = "C",
	['taskComplexPickupObject'] = "C",
	['cancelOverrideRestart'] = "C",
	['setCharHasUsedEntryExit'] = "C",
	['renderBegin'] = "C",
	['createPlayer'] = "C",
	['setPlay3dAudioStreamAtObject'] = "C",
	['switchRubbish'] = "C",
	['sampStorePlayerIncarData'] = "C",
	['setCharVisible'] = "C",
	['setPedDensityMultiplier'] = "C",
	['findMaxNumberOfGroupMembers'] = "C",
	['getActiveInterior'] = "C",
	['doesFileExist'] = "C",
	['dontRemoveObject'] = "C",
	['initZonePopulationSettings'] = "C",
	['dxutGetListboxSelectedItemAndCount'] = "C",
	['isExplosionInArea'] = "C",
	['addToObjectVelocity'] = "C",
	['sampGetCharHandleBySampPlayerId'] = "C",
	['dxutGetSliderValue'] = "C",
	['dxutAddSlider'] = "C",
	['lockPlayerControl'] = "C",
	['setCharAnimSpeed'] = "C",
	['givePlayerClothes'] = "C",
	['dxutSetDialogBackgroundColor'] = "C",
	['dxutIsCheckboxChecked'] = "C",
	['getCarModel'] = "C",
	['dxutSetControlVisible'] = "C",
	['createFxSystemOnCar'] = "C",
	['removePriceModifier'] = "C",
	['raknetSendBitStream'] = "C",
	['removeWeaponFromChar'] = "C",
	['raknetSendRpc'] = "C",
	['getCurrentCharWeapon'] = "C",
	['getObjectMass'] = "C",
	['isTrailerAttachedToCab'] = "C",
	['sampGetBase'] = "C",
	['clearObjectLastWeaponDamage'] = "C",
	['isCurrentCharWeapon'] = "C",
	['getPadState'] = "C",
	['dxutSetDialogVisible'] = "C",
	['dxutGetDialogPosAndSize'] = "C",
	['dxutSetDialogPos'] = "C",
	['switchPedRoadsBackToOriginal'] = "C",
	['setPlayerPlayerTargetting'] = "C",
	['taskCarDriveWander'] = "C",
	['sampGetRpcCallbackByRpcId'] = "C",
	['isCarStopped'] = "C",
	['dxutAddButton'] = "C",
	['setVehicleToFadeIn'] = "C",
	['isCharInAnyCar'] = "C",
	['dxutPopEvent'] = "C",
	['taskGotoCharOffset'] = "C",
	['isObjectAttached'] = "C",
	['setMenuColumn'] = "C",
	['renderDrawBoxWithBorder'] = "C",
	['sampSendEnterVehicle'] = "C",
	['shakeCam'] = "C",
	['setFreebiesInVehicle'] = "C",
	['isKeyDown'] = "C",
	['renderReleaseFont'] = "C",
	['setCurrentCharWeapon'] = "C",
	['disablePlayerSprint'] = "C",
	['sampGetCursorMode'] = "C",
	['releaseAudioStream'] = "C",
	['sampSetCursorMode'] = "C",
	['sampIsCursorActive'] = "C",
	['setPlayerInCarCameraMode'] = "C",
	['createFxSystemOnChar'] = "C",
	['sampGetPlayerSpecialAction'] = "C",
	['setRadioToPlayersFavouriteStation'] = "C",
	['displayZoneNames'] = "C",
	['sampIsLocalPlayerSpawned'] = "C",
	['sampIsPlayerPaused'] = "C",
	['clearWantedLevelInGarage'] = "C",
	['createCharAtAttractor'] = "C",
	['sampToggleCursor'] = "C",
	['rawset'] = "C",
	['giveWeaponToChar'] = "C",
	['sampFindAnimationIdByNameAndFile'] = "C",
	['script_version'] = "C",
	['createFxSystemOnCharWithDirection'] = "C",
	['sampfuncsLog'] = "C",
	['sampGetPlayerAnimationId'] = "C",
	['setMissionAudioPosition'] = "C",
	['sampGetListboxItemsCount'] = "C",
	['sampGetPickupSampIdByHandle'] = "C",
	['changeBlipColour'] = "C",
	['isCharWaitingForWorldCollision'] = "C",
	['taskDriveBy'] = "C",
	['sampGetObjectSampIdByHandle'] = "C",
	['addSetPiece'] = "C",
	['finishSettingUpConversationNoSubtitles'] = "C",
	['sampGetObjectHandleBySampId'] = "C",
	['sampConnectToServer'] = "C",
	['sampGetCurrentDialogId'] = "C",
	['attachCharToCar'] = "C",
	['sampGetCurrentDialogType'] = "C",
	['sampSetCurrentDialogEditboxText'] = "C",
	['setEnterCarRangeMultiplier'] = "C",
	['setPlayerMood'] = "C",
	['getScriptFireCoords'] = "C",
	['findAllRandomVehiclesInSphere'] = "C",
	['printText'] = "C",
	['explodeCar'] = "C",
	['releasePathNodes'] = "C",
	['getObjectPointerHandle'] = "C",
	['wrap'] = "C", -- table: coroutine
	['yield'] = "C", -- table: coroutine
	['resume'] = "C", -- table: coroutine
	['status'] = "C", -- table: coroutine
	['isyieldable'] = "C", -- table: coroutine
	['running'] = "C", -- table: coroutine
	['create'] = "C", -- table: coroutine
	['sampSetCurrentDialogListItem'] = "C",
	['renderBindTexture'] = "C",
	['areAnyCarCheatsActivated'] = "C",
	['setTaxiLights'] = "C",
	['sampGetDialogInfoPtr'] = "C",
	['sampIs3dTextDefined'] = "C",
	['switchCarSiren'] = "C",
	['isObjectWithinBrainActivationRange'] = "C",
	['sampCreate3dText'] = "C",
	['setStructElement'] = "C",
	['isCharGettingInToACar'] = "C",
	['getPlayerInCarCameraMode'] = "C",
	['getDeadCharPickupCoords'] = "C",
	['raknetSendBitStreamEx'] = "C",
	['drawSphere'] = "C",
	['raknetSendRpcEx'] = "C",
	['activateInteriorPeds'] = "C",
	['isCharInAnySearchlight'] = "C",
	['removeAnimation'] = "C",
	['setCameraInFrontOfChar'] = "C",
	['isVehicleOnAllWheels'] = "C",
	['isCopVehicleInArea3dNoSave'] = "C",
	['reportMissionAudioEventAtChar'] = "C",
	['addStuntJump'] = "C",
	['hasObjectOfTypeBeenSmashed'] = "C",
	['rotateObject'] = "C",
	['sampGetGamestate'] = "C",
	['clearCharRelationship'] = "C",
	['fireHunterGun'] = "C",
	['setPlayersCanBeInSeparateCars'] = "C",
	['setObjectRecordsCollisions'] = "C",
	['setCutsceneAnim'] = "C",
	['clearObjectPath'] = "C",
	['sampGetPlayerColor'] = "C",
	['sampSendDamageVehicle'] = "C",
	['isCarSirenOn'] = "C",
	['sampSendSpawn'] = "C",
	['sampSendExitVehicle'] = "C",
	['setPlayerJumpButton'] = "C",
	['sampGetChatInputText'] = "C",
	['dxutAddListbox'] = "C",
	['isCharStoppedInAreaInCar2d'] = "C",
	['isCharInAngledAreaInCar3d'] = "C",
	['copyCharDecisionMaker'] = "C",
	['createPickupWithAmmo'] = "C",
	['storeCarCharIsIn'] = "C",
	['setHeliBladesFullSpeed'] = "C",
	['taskLookAtCoord'] = "C",
	['restartScmThread'] = "C",
	['convertQuaternionToMatrix'] = "C",
	['attachObjectToObject'] = "C",
	['getDebugCameraCoordinates'] = "C",
	['setMissionTrainCoordinates'] = "C",
	['hasCharBeenDamagedByWeapon'] = "C",
	['taskDie'] = "C",
	['getGameVersion'] = "C",
	['setObjectMass'] = "C",
	['setRadioChannel'] = "C",
	['sampGetPlayerPing'] = "C",
	['sampGetPlayerStructPtr'] = "C",
	['getZoneGangStrength'] = "C",
	['getRadioChannel'] = "C",
	['printHelpForever'] = "C",
	['getAudioStreamVolume'] = "C",
	['registerOddjobMissionPassed'] = "C",
	['hideCharWeaponForScriptedCutscene'] = "C",
	['sampDisconnectWithReason'] = "C",
	['addBlipForCar'] = "C",
	['isObjectInAngledArea3d'] = "C",
	['addAttractor'] = "C",
	['isObjectStatic'] = "C",
	['addCharDecisionMakerEventResponse'] = "C",
	['getRemoteControlledCar'] = "C",
	['sampGetPlayerHealth'] = "C",
	['setInterpolationParameters'] = "C",
	['sampGetPlayerArmor'] = "C",
	['clearCharLastDamageEntity'] = "C",
	['drawWeaponshopCorona'] = "C",
	['switchDeathPenalties'] = "C",
	['sampSetSendrate'] = "C",
	['sampGetStreamedOutPlayerPos'] = "C",
	['__gc'] = "C", -- table: lua_thread
	['terminate'] = "C", -- table: lua_thread
	['new'] = "C", -- table: lua_thread
	['status'] = "C", -- table: lua_thread
	['create'] = "C", -- table: lua_thread
	['create_suspended'] = "C", -- table: lua_thread
	['__eq'] = "C", -- table: lua_thread
	['__pairs'] = "C", -- table: lua_thread
	['run'] = "C", -- table: lua_thread
	['getParkingNodeInArea'] = "C",
	['sampSendInteriorChange'] = "C",
	['setCarCoordinates'] = "C",
	['getVehiclePointerHandle'] = "C",
	['setCreateRandomGangMembers'] = "C",
	['deleteAllTrains'] = "C",
	['dxutAddEditbox'] = "C",
	['sampSpawnPlayer'] = "C",
	['hasCarBeenDamagedByChar'] = "C",
	['isGamePaused'] = "C",
	['getCleoSharedVar'] = "C",
	['changePlaybackToUseAi'] = "C",
	['ignoreHeightDifferenceFollowingNodes'] = "C",
	['getD3DDevicePtr'] = "C",
	['playBeatTrack'] = "C",
	['addVelocityRelativeToObjectVelocity'] = "C",
	['setCarStatus'] = "C",
	['getScmThreadStructNamed'] = "C",
	['createRandomCarForCarPark'] = "C",
	['sampSendDeathByPlayer'] = "C",
	['hasCharBeenPhotographed'] = "C",
	['setPlay3dAudioStreamAtCar'] = "C",
	['locateObject2d'] = "C",
	['doesCharExist'] = "C",
	['getCarPitch'] = "C",
	['setAudioStreamLooped'] = "C",
	['sampGetTextdrawPoolPtr'] = "C",
	['cameraPersistTrack'] = "C",
	['pointSearchlightAtChar'] = "C",
	['setCarDensityMultiplier'] = "C",
	['sampSetLocalPlayerName'] = "C",
	['setUpConversationEndNodeWithScriptedSpeech'] = "C",
	['setMissionRespectTotal'] = "C",
	['setCarEngineOn'] = "C",
	['doesVehicleExist'] = "C",
	['load3dAudioStreamFromMemory'] = "C",
	['dropSecondObject'] = "C",
	['addExplosion'] = "C",
	['manageAllPopulation'] = "C",
	['load3dAudioStream'] = "C",
	['setCharSuffersCriticalHits'] = "C",
	['selectWeaponsForVehicle'] = "C",
	['taskClimb'] = "C",
	['popFloat'] = "C",
	['startNewCustomScript'] = "C",
	['setCharStayInCarWhenJacked'] = "C",
	['setSenseRange'] = "C",
	['makePlayerGangDisappear'] = "C",
	['moveSearchlightBetweenCoords'] = "C",
	['clearGxtEntry'] = "C",
	['__gc'] = "C", -- table: script
	['find'] = "C", -- table: script
	['list'] = "C", -- table: script
	['new'] = "C", -- table: script
	['pause'] = "C", -- table: script
	['__pairs'] = "C", -- table: script
	['reload'] = "C", -- table: script
	['load'] = "C", -- table: script
	['__eq'] = "C", -- table: script
	['unload'] = "C", -- table: script
	['resume'] = "C", -- table: script
	['get'] = "C", -- table: script
	['createRandomCharAsDriver'] = "C",
	['setCharGetOutUpsideDownCar'] = "C",
	['setCarLightsOn'] = "C",
	['getRandomCopInArea'] = "C",
	['getAvailableVehicleMod'] = "C",
	['xpcall'] = "C",
	['convertGameScreenCoordsToWindowScreenCoords'] = "C",
	['renderFontDrawText'] = "C",
	['shakePad'] = "C",
	['addStuckCarCheckWithWarp'] = "C",
	['setFreeGxtEntry'] = "C",
	['renderDrawLine'] = "C",
	['renderGetFontDrawTextLength'] = "C",
	['sampSetChatInputText'] = "C",
	['taskGoStraightToCoord'] = "C",
	['getCursorPos'] = "C",
	['isObjectOnScreen'] = "C",
	['renderCreateFont'] = "C",
	['addBlipForPickup'] = "C",
	['renderReleaseTexture'] = "C",
	['setCharNeverLeavesGroup'] = "C",
	['isPlayerTargettingObject'] = "C",
	['copySharedCharDecisionMaker'] = "C",
	['taskCarMission'] = "C",
	['getFolderPath'] = "C",
	['highlightMenuItem'] = "C",
	['setDisableMilitaryZones'] = "C",
	['isCarStoppedInArea2d'] = "C",
	['setCharWantedByPolice'] = "C",
	['giveMeleeAttackToChar'] = "C",
	['setCharInterior'] = "C",
	['getWorkingDirectory'] = "C",
	['removeScriptFire'] = "C",
	['loadfile'] = "C",
	['setCharCanBeShotInVehicle'] = "C",
	['activateSaveMenu'] = "C",
	['setCarCanGoAgainstTraffic'] = "C",
	['taskLookAtObject'] = "C",
	['setTextWrapx'] = "C",
	['taskSmartFleeChar'] = "C",
	['attachCharToBike'] = "C",
	['taskScratchHead'] = "C",
	['isCharStoppedInAngledAreaInCar2d'] = "C",
	['getClosestStraightRoad'] = "C",
	['renderGetTextureSprite'] = "C",
	['renderDrawBox'] = "C",
	['renderDrawTexture'] = "C",
	['getObjectVelocity'] = "C",
	['startCharFire'] = "C",
	['setCharProofs'] = "C",
	['renderSetTexCoord'] = "C",
	['writeMemory'] = "C",
	['renderVertex'] = "C",
	['renderColor'] = "C",
	['renderEnd'] = "C",
	['renderGetTextureStruct'] = "C",
	['loadMissionText'] = "C",
	['printString'] = "C",
	['getGxtText'] = "C",
	['setLoadCollisionForCharFlag'] = "C",
	['displayTextWithNumber'] = "C",
	['isVehicleInSearchlight'] = "C",
	['sampSetChatInputEnabled'] = "C",
	['renderGetFontDrawHeight'] = "C",
	['isProjectileInArea'] = "C",
	['setBoatCruiseSpeed'] = "C",
	['doFade'] = "C",
	['switchPedRoadsOff'] = "C",
	['getCarCoordinates'] = "C",
	['getZoneDealerStrength'] = "C",
	['locateCharAnyMeansCar2d'] = "C",
	['displayTextWith2Numbers'] = "C",
	['getVehicleModType'] = "C",
	['removeStuckCarCheck'] = "C",
	['setCharSwimSpeed'] = "C",
	['switchAudioZone'] = "C",
	['attachCarToCar'] = "C",
	['useRenderCommands'] = "C",
	['convert3DCoordsToScreenEx'] = "C",
	['createDirectory'] = "C",
	['loadTextureDictionary'] = "C",
	['module'] = "C",
	['locateCharOnFootChar3d'] = "C",
	['getCharInCarPassengerSeat'] = "C",
	['drawRect'] = "C",
	['drawSprite'] = "C",
	['isGroupLeader'] = "C",
	['createRandomCharAsPassenger'] = "C",
	['switchStreaming'] = "C",
	['script_url'] = "C",
	['explodeCarInCutsceneShakeAndBits'] = "C",
	['isCharStuckUnderCar'] = "C",
	['buyItem'] = "C",
	['isPcUsingJoypad'] = "C",
	['getPcMouseMovement'] = "C",
	['createForsalePropertyPickup'] = "C",
	['setTargetCarForMissionGarage'] = "C",
	['allowPauseInWidescreen'] = "C",
	['taskCarTempAction'] = "C",
	['fixCarTire'] = "C",
	['taskDuck'] = "C",
	['getHavocCausedByPlayer'] = "C",
	['sampGetPickupHandleBySampId'] = "C",
	['switchObjectBrains'] = "C",
	['isCharInAnyPlane'] = "C",
	['setMenuColumnOrientation'] = "C",
	['carGotoCoordinatesRacing'] = "C",
	['getRidOfPlayerProstitute'] = "C",
	['removeUser3dMarker'] = "C",
	['isButtonPressed'] = "C",
	['createUser3dMarker'] = "C",
	['locateCharInCarObject2d'] = "C",
	['getDebugCameraPointAt'] = "C",
	['setScriptCoopGame'] = "C",
	['activatePimpCheat'] = "C",
	['initialiseObjectPath'] = "C",
	['getAudioStreamState'] = "C",
	['loadCharDecisionMaker'] = "C",
	['sampGetMaxPlayerId'] = "C",
	['isCarVisiblyDamaged'] = "C",
	['isMessageBeingDisplayed'] = "C",
	['isLastBuildingModelShotByPlayer'] = "C",
	['forceAllVehicleLightsOff'] = "C",
	['doesGroupExist'] = "C",
	['printTextNow'] = "C",
	['flushRoute'] = "C",
	['gameClock'] = "C",
	['do2dRectanglesCollide'] = "C",
	['isSkipWaitingForScriptToFadeIn'] = "C",
	['setUsesCollisionOfClosestObjectOfType'] = "C",
	['carWanderRandomly'] = "C",
	['makePlayerGangReappear'] = "C",
	['setUpSkipForVehicleFinishedByScript'] = "C",
	['isCarStuckOnRoof'] = "C",
	['storeCarCharIsAttachedToNoSave'] = "C",
	['performSequenceTask'] = "C",
	['setAudioStreamVolume'] = "C",
	['makeObjectTargettable'] = "C",
	['fixCar'] = "C",
	['dxutIsDialogExists'] = "C",
	['taskFollowPathNodesToCoordWithRadius'] = "C",
	['drawSubtitlesBeforeFade'] = "C",
	['clearAllCharRelationships'] = "C",
	['isWidescreenOnInOptions'] = "C",
	['removeStreamedScript'] = "C",
	['isCharHeadMissing'] = "C",
	['setUpSkip'] = "C",
	['isPlayerClimbing'] = "C",
	['anchorBoat'] = "C",
	['getTerritoryUnderControlPercentage'] = "C",
	['loadMissionAudio'] = "C",
	['setDeathWeaponsPersist'] = "C",
	['sampfuncsUnregisterConsoleCommand'] = "C",
	['locateCharAnyMeansObject2d'] = "C",
	['activateGarage'] = "C",
	['changeCarColourFromMenu'] = "C",
	['improveCarByCheating'] = "C",
	['printHelpString'] = "C",
	['displayOnscreenCounterWithString'] = "C",
	['cameraSetLerpFov'] = "C",
	['setPlayerGroupToFollowAlways'] = "C",
	['incrementFloatStatNoMessage'] = "C",
	['freezeObjectPosition'] = "C",
	['playerLeftCrane'] = "C",
	['isCharTouchingChar'] = "C",
	['addShortRangeSpriteBlipForContactPoint'] = "C",
	['getCurrentLanguage'] = "C",
	['removeCharFromCarMaintainPosition'] = "C",
	['getShoppingExtraInfo'] = "C",
	['setUpConversationNodeWithScriptedSpeech'] = "C",
	['overrideNextRestart'] = "C",
	['setPlayerEnterCarButton'] = "C",
	['processLineOfSight'] = "C",
	['taskSmartFleePoint'] = "C",
	['fireSingleBullet'] = "C",
	['attachMissionAudioToCar'] = "C",
	['script_author'] = "C",
	['removeOilPuddlesInArea'] = "C",
	['removeCharElegantly'] = "C",
	['loadSpecialModel'] = "C",
	['setNoResprays'] = "C",
	['loadAudioStreamFromMemory'] = "C",
	['freeMemory'] = "C",
	['setPlayerFastReload'] = "C",
	['sampSetChatString'] = "C",
	['isThisModelAPlane'] = "C",
	['setExtraCarColours'] = "C",
	['setObjectCoordinatesAndVelocity'] = "C",
	['incrementIntStatNoMessage'] = "C",
	['getPriceOfItem'] = "C",
	['isCharDucking'] = "C",
	['clearThisPrintBigNow'] = "C",
	['isPlayerUsingJetpack'] = "C",
	['playerEnteredQuarryCrane'] = "C",
	['isCarUpright'] = "C",
	['loadSceneInDirection'] = "C",
	['getDeadCharCoordinates'] = "C",
	['loadShop'] = "C",
	['shutCharUpForScriptedSpeech'] = "C",
	['setVisibilityOfClosestObjectOfType'] = "C",
	['createGroup'] = "C",
	['newproxy'] = "C",
	['taskPickUpSecondObject'] = "C",
	['isNextStationAllowed'] = "C",
	['isGangWarFightingGoingOn'] = "C",
	['allocateStreamedScriptToObject'] = "C",
	['wasKeyReleased'] = "C",
	['setCarCollision'] = "C",
	['isThisModelACar'] = "C",
	['doesPickupExist'] = "C",
	['isCarOnScreen'] = "C",
	['setCharHealth'] = "C",
	['getStringWidth'] = "C",
	['setTextBackground'] = "C",
	['setObjectRotation'] = "C",
	['isObjectIntersectingWorld'] = "C",
	['hasGameJustReturnedFromFrontend'] = "C",
	['attachTrailerToCab'] = "C",
	['isPlayerInPositionForConversation'] = "C",
	['reportMissionAudioEventAtCar'] = "C",
	['isCharInAngledAreaOnFoot2d'] = "C",
	['storeClosestEntities'] = "C",
	['shutAllCharsUp'] = "C",
	['getCharHealth'] = "C",
	['isCarOnFire'] = "C",
	['isCharTouchingVehicle'] = "C",
	['setCleoSharedVar'] = "C",
	['doesDecisionMakerExist'] = "C",
	['getDynamicLibraryProcedure'] = "C",
	['sampGetCurrentServerAddress'] = "C",
	['resetVehicleCameraTweak'] = "C",
	['getClosestCarNodeWithHeading'] = "C",
	['script_description'] = "C",
	['forceBigMessageAndCounter'] = "C",
	['isGameWindowForeground'] = "C",
	['hasCharSpottedCharInFront'] = "C",
	['sampSendPickedUpPickup'] = "C",
	['getTimeStepValue'] = "C",
	['isWantedLevelGreater'] = "C",
	['setVehicleDirtLevel'] = "C",
	['displayRadar'] = "C",
	['giveNonPlayerCarNitro'] = "C",
	['throwCppException'] = "C",
	['getCharActiveInterior'] = "C",
	['switchEntryExit'] = "C",
	['setPoolTableCoords'] = "C",
	['setLaRiots'] = "C",
	['isCharMale'] = "C",
	['drawCrosshair'] = "C",
	['setAircraftCarrierSamSite'] = "C",
	['findTrainDirection'] = "C",
	['getOffsetFromCarInWorldCoords'] = "C",
	['createCarGeneratorWithPlate'] = "C",
	['getCarModelValue'] = "C",
	['setUpSkipForSpecificVehicle'] = "C",
	['taskDead'] = "C",
	['setMenuColumnWidth'] = "C",
	['isCharInAreaOnFoot3d'] = "C",
	['isMoneyPickupAtCoords'] = "C",
	['setCharSayScript'] = "C",
	['setGroupFollowStatus'] = "C",
	['setPlayerNeverGetsTired'] = "C",
	['attachMissionAudioToObject'] = "C",
	['useDetonator'] = "C",
	['getCarHeading'] = "C",
	['enableAmbientCrime'] = "C",
	['taskGotoChar'] = "C",
	['setRespawnPointForDurationOfMission'] = "C",
	['sampShowDialog'] = "C",
	['isFlameInAngledArea2d'] = "C",
	['downloadUrlToFile'] = "C",
	['getCharVelocity'] = "C",
	['setVirtualKeyDown'] = "C",
	['doesScriptFireExist'] = "C",
	['setTrainForcedToSlowDown'] = "C",
	['doesObjectHaveThisModel'] = "C",
	['setCarAlwaysCreateSkids'] = "C",
	['taskToggleDuck'] = "C",
	['setRelationship'] = "C",
	['isClosestObjectOfTypeSmashedOrDamaged'] = "C",
	['taskCharSlideToCoord'] = "C",
	['getObjectHealth'] = "C",
	['setAreaName'] = "C",
	['getTrainCaboose'] = "C",
	['areSubtitlesSwitchedOn'] = "C",
	['setAllCarsCanBeDamaged'] = "C",
	['createBirds'] = "C",
	['convertScreenCoordsToWorld3D'] = "C",
	['setPlayerModel'] = "C",
	['getLevelDesignCoordsForObject'] = "C",
	['setPetrolTankWeakpoint'] = "C",
	['setCharVelocity'] = "C",
	['locateCharOnFootChar2d'] = "C",
	['getMinutesToTimeOfDay'] = "C",
	['addSphere'] = "C",
	['displayText'] = "C",
	['getRandomCarOfTypeInAngledAreaNoSave'] = "C",
	['setForceRandomCarModel'] = "C",
	['sampCreate3dTextEx'] = "C",
	['setGunshotSenseRangeForRiot2'] = "C",
	['getTotalNumberOfPedsKilledByPlayer'] = "C",
	['sampSetClientCommandDescription'] = "C",
	['markCharAsNoLongerNeeded'] = "C",
	['setZoneNoCops'] = "C",
	['renderLoadTextureFromFile'] = "C",
	['taskEnterCarAsDriver'] = "C",
	['setCharCoordinatesDontWarpGang'] = "C",
	['playerEnteredBuildingsiteCrane'] = "C",
	['breakObject'] = "C",
	['setGangWarsTrainingMission'] = "C",
	['setCameraPositionUnfixed'] = "C",
	['loadSplashScreen'] = "C",
	['getRandomCarModelInMemory'] = "C",
	['setVehicleIsConsideredByPlayer'] = "C",
	['createLockedPropertyPickup'] = "C",
	['taskFollowFootsteps'] = "C",
	['addNextMessageToPreviousBriefs'] = "C",
	['isPlayerTargettingChar'] = "C",
	['getCutsceneTime'] = "C",
	['sampIsPlayerNpc'] = "C",
	['setVehicleInterior'] = "C",
	['killFxSystem'] = "C",
	['hideAllFrontendBlips'] = "C",
	['randomPassengerSay'] = "C",
	['getPlaneUndercarriagePosition'] = "C",
	['getHashKey'] = "C",
	['isCharInAreaInCar2d'] = "C",
	['controlCarDoor'] = "C",
	['takeRemoteControlOfCar'] = "C",
	['showBlipsOnAllLevels'] = "C",
	['setUpConversationNodeWithSpeech'] = "C",
	['drawLightWithRange'] = "C",
	['taskUseAttractor'] = "C",
	['attachWinchToHeli'] = "C",
	['setCheckpointCoords'] = "C",
	['isCarPassengerSeatFree'] = "C",
	['setCreateRandomCops'] = "C",
	['setCharCollision'] = "C",
	['freezeCarPosition'] = "C",
	['taskGoToObject'] = "C",
	['restoreCameraJumpcut'] = "C",
	['setHelpMessageBoxSize'] = "C",
	['taskLeaveAnyCar'] = "C",
	['cameraSetShakeSimulationSimple'] = "C",
	['skipCutsceneEnd'] = "C",
	['getPercentageTaggedInArea'] = "C",
	['getNearestTagPosition'] = "C",
	['getDistanceBetweenCoords3d'] = "C",
	['addSpriteBlipForCoord'] = "C",
	['awardPlayerMissionRespect'] = "C",
	['getAudioStreamLength'] = "C",
	['isSampLoaded'] = "C",
	['setCharWeaponSkill'] = "C",
	['addShortRangeSpriteBlipForCoord'] = "C",
	['switchRandomTrains'] = "C",
	['printBig'] = "C",
	['isCharOnScreen'] = "C",
	['setMenuItemWith2Numbers'] = "C",
	['raknetBitStreamResetReadPointer'] = "C",
	['attachCameraToVehicleLookAtVehicle'] = "C",
	['isLineOfSightClear'] = "C",
	['isNightVisionActive'] = "C",
	['getCurrentVehiclePaintjob'] = "C",
	['isCharInArea3d'] = "C",
	['pointCameraAtChar'] = "C",
	['setCharCoordinatesDontWarpGangNoOffset'] = "C",
	['decrementIntStat'] = "C",
	['removeRcBuggy'] = "C",
	['sampIsChatVisible'] = "C",
	['setOnlyCreateGangMembers'] = "C",
	['removeDecisionMaker'] = "C",
	['setObjectHealth'] = "C",
	['locateStoppedCar3d'] = "C",
	['setCharForceDieInCar'] = "C",
	['hasTrainDerailed'] = "C",
	['setFreeResprays'] = "C",
	['getNumCarColours'] = "C",
	['createRandomChar'] = "C",
	['convert3DCoordsToScreen'] = "C",
	['reportMissionAudioEventAtObject'] = "C",
	['reportMissionAudioEventAtPosition'] = "C",
	['sampDestroy3dText'] = "C",
	['isCarUpsidedown'] = "C",
	['killFxSystemNow'] = "C",
	['getLoadedShop'] = "C",
	['isEmergencyServicesVehicle'] = "C",
	['setCharCoordinatesNoOffset'] = "C",
	['updatePickupMoneyPerDay'] = "C",
	['locateStoppedCharOnFoot2d'] = "C",
	['forceDeathRestart'] = "C",
	['getGameTimer'] = "C",
	['isCarStreetRacer'] = "C",
	['isGroupMember'] = "C",
	['detachCar'] = "C",
	['getCharAnimTotalTime'] = "C",
	['setUpConversationEndNodeWithSpeech'] = "C",
	['storeCarModState'] = "C",
	['taskFallAndGetUp'] = "C",
	['switchPoliceHelis'] = "C",
	['consumeWindowMessage'] = "C",
	['isCharInAngledAreaInCar2d'] = "C",
	['getRandomCharInZone'] = "C",
	['removeVehicleMod'] = "C",
	['pointCameraAtPoint'] = "C",
	['makePlayerSafe'] = "C",
	['hasResprayHappened'] = "C",
	['locateCharAnyMeansChar3d'] = "C",
	['startCharFacialTalk'] = "C",
	['getClosestStealableObject'] = "C",
	['clearMissionAudio'] = "C",
	['dxutGetControlText'] = "C",
	['heliGotoCoords'] = "C",
	['carSetIdle'] = "C",
	['hasObjectBeenPhotographed'] = "C",
	['getDoorAngleRatio'] = "C",
	['setCharDruggedUp'] = "C",
	['isMouseUsingVerticalInversion'] = "C",
	['hasObjectBeenUprooted'] = "C",
	['get2dLinesIntersectPoint'] = "C",
	['addSparks'] = "C",
	['setMessageFormatting'] = "C",
	['setTextCentre'] = "C",
	['sampTextdrawIsExists'] = "C",
	['getRandomCarOfTypeInArea'] = "C",
	['attachObjectToCar'] = "C",
	['enableCharSpeech'] = "C",
	['getPickupCoordinates'] = "C",
	['setCarHeading'] = "C",
	['taskAimGunAtChar'] = "C",
	['raknetEmulPacketReceiveBitStream'] = "C",
	['isCharTalking'] = "C",
	['getDriverOfCar'] = "C",
	['isAttachedPlayerHeadingAchieved'] = "C",
	['isCharStoppedInAreaInCar3d'] = "C",
	['getNameOfEntryExitCharUsed'] = "C",
	['syncWater'] = "C",
	['storeClothesState'] = "C",
	['setBlipCoordinates'] = "C",
	['boatStop'] = "C",
	['isPlayerPerformingStoppie'] = "C",
	['taskLookAtChar'] = "C",
	['getNumOfModelsKilledByPlayer'] = "C",
	['fixCarDoor'] = "C",
	['isCharStoppedInArea3d'] = "C",
	['setTrainCruiseSpeed'] = "C",
	['openCarDoorABit'] = "C",
	['attachMissionAudioToChar'] = "C",
	['addExplosionVariableShake'] = "C",
	['getCutsceneOffset'] = "C",
	['setNightVision'] = "C",
	['registerMissionPassed'] = "C",
	['showCursor'] = "C",
	['setSearchlightClipIfColliding'] = "C",
	['forceInteriorLightingForPlayer'] = "C",
	['setCharFireDamageMultiplier'] = "C",
	['input'] = "C", -- table: io
	['tmpfile'] = "C", -- table: io
	['read'] = "C", -- table: io
	['output'] = "C", -- table: io
	['open'] = "C", -- table: io
	['close'] = "C", -- table: io
	['write'] = "C", -- table: io
	['popen'] = "C", -- table: io
	['flush'] = "C", -- table: io
	['type'] = "C", -- table: io
	['lines'] = "C", -- table: io
	['setCharAnimCurrentTime'] = "C",
	['setGarageResprayFree'] = "C",
	['cameraSetVectorMove'] = "C",
	['hasCarBeenDamagedByCar'] = "C",
	['getCharAnimCurrentTime'] = "C",
	['gcinfo'] = "C",
	['cameraPersistFov'] = "C",
	['isPlayerControlLocked'] = "C",
	['cameraPersistPos'] = "C",
	['getWaterHeightAtCoords'] = "C",
	['incrementFloatStat'] = "C",
	['setFixedCameraPosition'] = "C",
	['clearCharTasks'] = "C",
	['setTextDrawBeforeFade'] = "C",
	['alterWantedLevelNoDrop'] = "C",
	['getCarCurrentGear'] = "C",
	['setGenerateCarsAroundCamera'] = "C",
	['lockCarDoors'] = "C",
	['extendRoute'] = "C",
	['switchOnGroundSearchlight'] = "C",
	['allocateStreamedScriptToRandomPed'] = "C",
	['findFirstFile'] = "C",
	['isPlayerTargettingAnything'] = "C",
	['playerEnteredDockCrane'] = "C",
	['getNumberOfInstancesOfStreamedScript'] = "C",
	['setCharBulletproofVest'] = "C",
	['setPlay3dAudioStreamAtChar'] = "C",
	['changeCarColour'] = "C",
	['restoreCarModState'] = "C",
	['sampCloseCurrentDialogWithButton'] = "C",
	['setAudioStreamState'] = "C",
	['getNameOfVehicleModel'] = "C",
	['winchCanPickObjectUp'] = "C",
	['isCarInWater'] = "C",
	['isCharTouchingObjectOnFoot'] = "C",
	['setWeatherToAppropriateTypeNow'] = "C",
	['createHorseshoePickup'] = "C",
	['markStreamedScriptAsNoLongerNeeded'] = "C",
	['deleteChar'] = "C",
	['setActiveMenuItem'] = "C",
	['clearSpecificZonesToTriggerGangWar'] = "C",
	['getObjectTurnMass'] = "C",
	['isCharSwimming'] = "C",
	['locateCharAnyMeans2d'] = "C",
	['removeSound'] = "C",
	['script_properties'] = "C",
	['lockDoor'] = "C",
	['grabEntityOnRopeForObject'] = "C",
	['dxutIsDialogVisible'] = "C",
	['locateCharOnFootCar3d'] = "C",
	['cameraIsVectorMoveRunning'] = "C",
	['isHelpMessageBeingDisplayed'] = "C",
	['isCharRespondingToEvent'] = "C",
	['setHeathazeEffect'] = "C",
	['isCharStoppedInAreaOnFoot3d'] = "C",
	['setCameraZoom'] = "C",
	['addUpsidedownCarCheck'] = "C",
	['showUpdateStats'] = "C",
	['startPlaybackRecordedCar'] = "C",
	['makeRoomInPlayerGangForMissionPeds'] = "C",
	['isCharOnFoot'] = "C",
	['warpCharFromCarToCoord'] = "C",
	['sampRegisterChatCommand'] = "C",
	['taskWarpCharIntoCarAsDriver'] = "C",
	['vehicleCanBeTargettedByHsMissile'] = "C",
	['setCutsceneModelTexture'] = "C",
	['setObjectPathSpeed'] = "C",
	['pauseCurrentBeatTrack'] = "C",
	['hasCarBeenDamagedByWeapon'] = "C",
	['makeHeliComeCrashingDown'] = "C",
	['openSequenceTask'] = "C",
	['setCharDrownsInWater'] = "C",
	['setMenuItemWithNumber'] = "C",
	['hasObjectBeenDamaged'] = "C",
	['sampSendOnfootData'] = "C",
	['printWith4NumbersNow'] = "C",
	['getVehicleClass'] = "C",
	['sampSendScmEvent'] = "C",
	['increasePlayerMaxHealth'] = "C",
	['isCharStoppedInAngledAreaInCar3d'] = "C",
	['setObjectAsStealable'] = "C",
	['getPlayerGroup'] = "C",
	['maxn'] = "C", -- table: table
	['move'] = "L", -- table: table
	['pack'] = "C", -- table: table
	['foreach'] = "L", -- table: table
	['sort'] = "C", -- table: table
	['remove'] = "L", -- table: table
	['foreachi'] = "L", -- table: table
	['unpack'] = "C", -- table: table
	['getn'] = "L", -- table: table
	['concat'] = "C", -- table: table
	['insert'] = "C", -- table: table
	['disableAllEntryExits'] = "C",
	['setPlaneUndercarriageUp'] = "C",
	['createMenu'] = "C",
	['applyForceToCar'] = "C",
	['isObjectInAngledArea2d'] = "C",
	['setGroupDecisionMaker'] = "C",
	['findNumberTagsTagged'] = "C",
	['locateCar3d'] = "C",
	['setExtraPoliceStationRestartPoint'] = "C",
	['playAndKillFxSystem'] = "C",
	['makePlayerFireProof'] = "C",
	['setExtraHospitalRestartPoint'] = "C",
	['hasImportGarageSlotBeenFilled'] = "C",
	['isCarStoppedInArea3d'] = "C",
	['raknetDeleteBitStream'] = "C",
	['deleteMenu'] = "C",
	['setCameraInFrontOfPlayer'] = "C",
	['getMenuItemAccepted'] = "C",
	['select'] = "C",
	['getCarCharIsUsing'] = "C",
	['getObjectQuaternion'] = "C",
	['playFxSystem'] = "C",
	['setSpritesDrawBeforeFade'] = "C",
	['spawnVehicleByCheating'] = "C",
	['setObjectScale'] = "C",
	['setCharSignalAfterKill'] = "C",
	['freezeCarPositionAndDontLoadCollision'] = "C",
	['switchSecurityCamera'] = "C",
	['setCharCanBeKnockedOffBike'] = "C",
	['setZoneForGangWarsTraining'] = "C",
	['taskTogglePedThreatScanner'] = "C",
	['closeSequenceTask'] = "C",
	['taskCarDriveToCoord'] = "C",
	['setInfraredVision'] = "C",
	['isCharStoppedInAreaOnFoot2d'] = "C",
	['setCharUsesUpperbodyDamageAnimsOnly'] = "C",
	['setCharMaxHealth'] = "C",
	['representIntAsFloat'] = "C",
	['dxutSetDialogMinimized'] = "C",
	['sampTextdrawSetLetterSizeAndColor'] = "C",
	['getActiveCameraPointAt'] = "C",
	['pausePlaybackRecordedCar'] = "C",
	['sampGetMiscInfoPtr'] = "C",
	['getCarPointer'] = "C",
	['isObjectPlayingAnim'] = "C",
	['canTriggerGangWarWhenOnAMission'] = "C",
	['planeAttackPlayerUsingDogFight'] = "C",
	['addArmourToChar'] = "C",
	['taskUseClosestMapAttractor'] = "C",
	['renderSetRenderState'] = "C",
	['isVehicleTouchingObject'] = "C",
	['switchCarEngine'] = "C",
	['doWeaponStuffAtStartOf2pGame'] = "C",
	['taskAimGunAtCoord'] = "C",
	['setCharOnlyDamagedByPlayer'] = "C",
	['setTimerBeepCountdownTime'] = "C",
	['setTimeOneDayForward'] = "C",
	['addBlipForCoord'] = "C",
	['setCarEscortCarFront'] = "C",
	['isPointObscuredByAMissionEntity'] = "C",
	['taskPlayAnimWithFlags'] = "C",
	['removeUpsidedownCarCheck'] = "C",
	['addBlipForDeadChar'] = "C",
	['setGroupSequence'] = "C",
	['removeWaypoint'] = "C",
	['isCharInModel'] = "C",
	['getSampfuncsGlobalVar'] = "C",
	['planeFlyInDirection'] = "C",
	['setHeadingLimitForAttachedChar'] = "C",
	['clearLoadedShop'] = "C",
	['setPlayerIsInStadium'] = "C",
	['printHelp'] = "C",
	['turnCarToFaceCoord'] = "C",
	['script_moonloader'] = "C",
	['setGangWarsActive'] = "C",
	['setObjectOnlyDamagedByPlayer'] = "C",
	['setCarAsMissionCar'] = "C",
	['removeAllCharWeapons'] = "C",
	['setZonePopulationRace'] = "C",
	['clearExtraColours'] = "C",
	['clearAllScriptFireFlags'] = "C",
	['getCharSwimState'] = "C",
	['isOpcodesAvailable'] = "C",
	['setCharCantBeDraggedOut'] = "C",
	['sampTextdrawGetProportional'] = "C",
	['taskKindaStayInSamePlace'] = "C",
	['attachCameraToChar'] = "C",
	['findAllRandomCharsInSphere'] = "C",
	['createEmergencyServicesCar'] = "C",
	['drawShadow'] = "C",
	['addBlipForChar'] = "C",
	['deleteCar'] = "C",
	['taskWalkAlongsideChar'] = "C",
	['getSoundLevelAtCoords'] = "C",
	['setHeliReachedTargetDistance'] = "C",
	['isPlayerPressingHorn'] = "C",
	['taskLeaveCarImmediately'] = "C",
	['setVehicleCanBeTargetted'] = "C",
	['carGotoCoordinates'] = "C",
	['freezeCharPositionAndDontLoadCollision'] = "C",
	['taskKillCharOnFootWhileDucking'] = "C",
	['getNameOfZone'] = "C",
	['getCityPlayerIsIn'] = "C",
	['setAnimGroupForChar'] = "C",
	['loadAudioStream'] = "C",
	['addContinuousSound'] = "C",
	['isCarDead'] = "C",
	['giveRemoteControlledModelToPlayer'] = "C",
	['setCharRotation'] = "C",
	['isCharHoldingObject'] = "C",
	['attachCameraToVehicle'] = "C",
	['alterWantedLevel'] = "C",
	['isPs2KeyboardKeyJustPressed'] = "C",
	['getBeatTrackStatus'] = "C",
	['controlMovableVehiclePart'] = "C",
	['sampTextdrawGetModelRotationZoomVehColor'] = "C",
	['forceWeather'] = "C",
	['getObjectPointer'] = "C",
	['setMaxFireGenerations'] = "C",
	['connectLods'] = "C",
	['readKillFrenzyStatus'] = "C",
	['setTextScale'] = "C",
	['dofile'] = "C",
	['raknetBitStreamWriteBitStream'] = "C",
	['setHeliOrientation'] = "C",
	['getProgressPercentage'] = "C",
	['createObject'] = "C",
	['error'] = "C",
	['getGroupSize'] = "C",
	['limitAngle'] = "C",
	['script_authors'] = "C",
	['removeCharFromGroup'] = "C",
	['setCarHydraulics'] = "C",
	['isThisModelAHeli'] = "C",
	['locateCharAnyMeansObject3d'] = "C",
	['flashHudObject'] = "C",
	['getAllObjects'] = "C",
	['getGameGlobalPtr'] = "C",
	['sampTextdrawSetPos'] = "C",
	['setCarEngineBroken'] = "C",
	['setTextEdge'] = "C",
	['createFxSystemOnCarWithDirection'] = "C",
	['getSequenceProgressRecursive'] = "C",
	['getCharHeightAboveGround'] = "C",
	['getObjectRotationVelocity'] = "C",
	['isCharInAir'] = "C",
	['isCharSittingInAnyCar'] = "C",
	['taskFollowPatrolRoute'] = "C",
	['getmetatable'] = "C",
	['setTextColour'] = "C",
	['setCharKindaStayInSamePlace'] = "C",
	['isCarTireBurst'] = "C",
	['sampTextdrawCreate'] = "C",
	['isCharUsingMapAttractor'] = "C",
	['taskCharSlideToCoordAndPlayAnim'] = "C",
	['clearAreaOfCars'] = "C",
	['taskJetpack'] = "C",
	['taskSitDown'] = "C",
	['doesDirectoryExist'] = "C",
	['getCameraFov'] = "C",
	['is2playerGameGoingOn'] = "C",
	['isCharInAngledAreaOnFoot3d'] = "C",
	['setFirstPersonInCarCameraMode'] = "C",
	['openCarDoor'] = "C",
	['setDarknessEffect'] = "C",
	['popCarBoot'] = "C",
	['controlCarHydraulics'] = "C",
	['getCharPlayerIsTargeting'] = "C",
	['isPlayerPerformingWheelie'] = "C",
	['ipairs'] = "C",
	['explodeCarInCutscene'] = "C",
	['getOffsetFromObjectInWorldCoords'] = "C",
	['getCityFromCoords'] = "C",
	['isSampfuncsConsoleActive'] = "C",
	['taskDrivePointRouteAdvanced'] = "C",
	['wait'] = "C",
	['taskPickUpObject'] = "C",
	['setPlay3dAudioStreamAtCoordinates'] = "C",
	['isModelInCdimage'] = "C",
	['setCharShootRate'] = "C",
	['isGarageOpen'] = "C",
	['setCarRotationVelocity'] = "C",
	['isCarInAirProper'] = "C",
	['createCutsceneObject'] = "C",
	['getRandomCharInSphereNoBrain'] = "C",
	['getCurrentDayOfWeek'] = "C",
	['listenToPlayerGroupCommands'] = "C",
	['dxutIsDialogMinimized'] = "C",
	['hasCarRecordingBeenLoaded'] = "C",
	['setCharBleeding'] = "C",
	['createSearchlightOnVehicle'] = "C",
	['fixCarPanel'] = "C",
	['requestCarRecording'] = "C",
	['taskFleePoint'] = "C",
	['markMissionTrainAsNoLongerNeeded'] = "C",
	['setArea51SamSite'] = "C",
	['giveRemoteControlledCarToPlayer'] = "C",
	['taskSetCharDecisionMaker'] = "C",
	['activateHeliSpeedCheat'] = "C",
	['setPlayerGroupRecruitment'] = "C",
	['displayOnscreenTimer'] = "C",
	['taskLookAtVehicle'] = "C",
	['taskAchieveHeading'] = "C",
	['clearGroupDecisionMakerEventResponse'] = "C",
	['getActiveCameraCoordinates'] = "C",
	['getBeatProximity'] = "C",
	['detachTrailerFromCab'] = "C",
	['sampHasDialogRespond'] = "C",
	['load'] = "C",
	['grabEntityOnWinch'] = "C",
	['buildPlayerModel'] = "C",
	['doesCarHaveHydraulics'] = "C",
	['isCharInAreaOnFoot2d'] = "C",
	['taskKillCharOnFootTimed'] = "C",
	['setCanResprayCar'] = "C",
	['freeDynamicLibrary'] = "C",
	['deletePlayer'] = "C",
	['performSequenceTaskFromProgress'] = "C",
	['takePhoto'] = "C",
	['requestVehicleMod'] = "C",
	['getCarForwardX'] = "C",
	['setClosestEntryExitFlag'] = "C",
	['releaseEntityFromRopeForObject'] = "C",
	['restoreClothesState'] = "C",
	['isFlameInAngledArea3d'] = "C",
	['setCharUsesCollisionClosestObjectOfType'] = "C",
	['releaseEntityFromWinch'] = "C",
	['getWeapontypeSlot'] = "C",
	['setCarTraction'] = "C",
	['hasObjectBeenDamagedByWeapon'] = "C",
	['heliKeepEntityInView'] = "C",
	['dxutListboxDeleteItem'] = "C",
	['setBlipAsFriendly'] = "C",
	['removeIpl'] = "C",
	['requestIpl'] = "C",
	['deleteSearchlight'] = "C",
	['taskGotoCar'] = "C",
	['registerFastestTime'] = "C",
	['printWith6Numbers'] = "C",
	['setRadarZoom'] = "C",
	['planeStartsInAir'] = "C",
	['taskGoToCoordWhileShooting'] = "C",
	['startCarFire'] = "C",
	['sampIsPlayerConnected'] = "C",
	['setSampfuncsConsoleCommandDescription'] = "C",
	['testCheat'] = "C",
	['getTimeOfDay'] = "C",
	['drawSpriteWithRotation'] = "C",
	['getfenv'] = "C",
	['setZonePopulationType'] = "C",
	['raknetGetRpcName'] = "C",
	['wasCutsceneSkipped'] = "C",
	['enableDisabledAttractorsOnObject'] = "C",
	['sampGetCurrentDialogEditboxText'] = "C",
	['playObjectAnim'] = "C",
	['unloadSpecialCharacter'] = "C",
	['locateCharInCarCar3d'] = "C",
	['extendPatrolRoute'] = "C",
	['flushPatrolRoute'] = "C",
	['getAllChars'] = "C",
	['getCharSpeed'] = "C",
	['taskFleeCharAnyMeans'] = "C",
	['setObjectVisible'] = "C",
	['raknetBitStreamReadString'] = "C",
	['sampTextdrawSetOutlineColor'] = "C",
	['clearHelp'] = "C",
	['removeObjectElegantly'] = "C",
	['addGroupDecisionMakerEventResponse'] = "C",
	['setCarCoordinatesNoOffset'] = "C",
	['getTrainCarriage'] = "C",
	['printWith2NumbersBig'] = "C",
	['addHospitalRestart'] = "C",
	['getRandomCharInSphere'] = "C",
	['finishSettingUpConversation'] = "C",
	['setPlayerCanDoDriveBy'] = "C",
	['startPlaybackRecordedCarLooped'] = "C",
	['setObjectAnimCurrentTime'] = "C",
	['dontSuppressCarModel'] = "C",
	['warpCharIntoCarAsPassenger'] = "C",
	['setCarRoll'] = "C",
	['isCharInTaxi'] = "C",
	['isCharInAreaInCar3d'] = "C",
	['switchCopsOnBikes'] = "C",
	['taskUseMobilePhone'] = "C",
	['policeHeliChaseEntity'] = "C",
	['isCharInAngledArea3d'] = "C",
	['getAmmoInCharWeapon'] = "C",
	['extinguishFireAtPoint'] = "C",
	['loadSpecialCharacter'] = "C",
	['setCarAvoidLevelTransitions'] = "C",
	['getRandomCarInSphereNoSave'] = "C",
	['storeWantedLevel'] = "C",
	['wasKeyPressed'] = "C",
	['sortOutObjectCollisionWithCar'] = "C",
	['startSettingUpConversation'] = "C",
	['setCarStayInSlowLane'] = "C",
	['planeFollowEntity'] = "C",
	['attachFxSystemToCharBone'] = "C",
	['clearSequenceTask'] = "C",
	['getNthClosestCarNodeWithHeading'] = "C",
	['planeAttackPlayer'] = "C",
	['hasModelLoaded'] = "C",
	['setObjectCoordinates'] = "C",
	['damageCarDoor'] = "C",
	['seeall'] = "C", -- table: package
	['loadlib'] = "C", -- table: package
	['searchpath'] = "C", -- table: package
	['dxutListboxInsertItem'] = "C",
	['heliFollowEntity'] = "C",
	['getCarMass'] = "C",
	['getCurrentCarMod'] = "C",
	['setCarStraightLineDistance'] = "C",
	['getCurrentDate'] = "C",
	['setRailtrackResistanceMult'] = "C",
	['locateStoppedCharOnFoot3d'] = "C",
	['freezeCharPosition'] = "C",
	['setCollectable1Total'] = "C",
	['attachCameraToVehicleLookAtChar'] = "C",
	['taskShootAtCoord'] = "C",
	['setGroupMember'] = "C",
	['createMissionTrain'] = "C",
	['renderLoadTextureFromFileInMemory'] = "C",
	['displayHud'] = "C",
	['getRandomCarOfTypeInAreaNoSave'] = "C",
	['setCarMission'] = "C",
	['getClosestCharNode'] = "C",
	['addBlood'] = "C",
	['getGameDirectory'] = "C",
	['isCharInCar'] = "C",
	['removeAllScriptFires'] = "C",
	['registerFloatStat'] = "C",
	['getFloatStat'] = "C",
	['setCarHealth'] = "C",
	['getDistanceBetweenCoords2d'] = "C",
	['isPlayerInShortcutTaxi'] = "C",
	['hasCarBeenResprayed'] = "C",
	['setCarCanBeVisiblyDamaged'] = "C",
	['markRoadNodeAsDontWander'] = "C",
	['clearWantedLevel'] = "C",
	['callFunction'] = "C",
	['drawWindow'] = "C",
	['applyBrakesToPlayersCar'] = "C",
	['taskSay'] = "C",
	['requestModel'] = "C",
	['deleteCheckpoint'] = "C",
	['getHeadingFromVector2d'] = "C",
	['getTargetBlipCoordinates'] = "C",
	['getPlayerChar'] = "C",
	['setTotalNumberOfMissions'] = "C",
	['sampStorePlayerPassengerData'] = "C",
	['addBlipForSearchlight'] = "C",
	['doesCarHaveStuckCarCheck'] = "C",
	['heliLandAtCoords'] = "C",
	['getWeapontypeModel'] = "C",
	['hasCharBeenArrested'] = "C",
	['getCarBlockingCar'] = "C",
	['setVehicleQuaternion'] = "C",
	['findNextFile'] = "C",
	['renderGetFontCharWidth'] = "C",
	['locateStoppedCharInCar2d'] = "C",
	['getMoonloaderVersion'] = "C",
	['locateCharAnyMeansChar2d'] = "C",
	['printStyledString'] = "C",
	['isCarInArea3d'] = "C",
	['clearCarLastDamageEntity'] = "C",
	['isCarInArea2d'] = "C",
	['warpCharIntoCar'] = "C",
	['damageCarPanel'] = "C",
	['isCharInAnyTrain'] = "C",
	['localClock'] = "C",
	['setWantedMultiplier'] = "C",
	['loadScene'] = "C",
	['resetVehicleHydraulics'] = "C",
	['sampSetGamestate'] = "C",
	['storeCarCharIsInNoSave'] = "C",
	['clearCarLastWeaponDamage'] = "C",
	['loadPathNodesInArea'] = "C",
	['isPointOnScreen'] = "C",
	['setUpsidedownCarNotDamaged'] = "C",
	['sampForcePassengerSyncSeatId'] = "C",
	['sampGetServerSettingsPtr'] = "C",
	['releaseWeather'] = "C",
	['addBlipForCoordOld'] = "C",
	['createSnapshotPickup'] = "C",
	['setCarEscortCarRight'] = "C",
	['burstCarTire'] = "C",
	['setCharKeyDown'] = "C",
	['hasMissionAudioLoaded'] = "C",
	['isRelationshipSet'] = "C",
	['createSwatRope'] = "C",
	['hasAnimationLoaded'] = "C",
	['setPlayerWeaponsScrollable'] = "C",
	['setObjectDynamic'] = "C",
	['printStringNow'] = "C",
	['areMeasurementsInMetres'] = "C",
	['sampIsChatCommandDefined'] = "C",
	['taskPlayAnimNonInterruptable'] = "C",
	['findAllRandomObjectsInSphere'] = "C",
	['setGroupSeparationRange'] = "C",
	['setCarFollowCar'] = "C",
	['setCoordBlipAppearance'] = "C",
	['setExtraColours'] = "C",
	['cameraSetVectorTrack'] = "C",
	['createCar'] = "C",
	['setObjectCollisionDamageEffect'] = "C",
	['require'] = "C",
	['popCarDoor'] = "C",
	['raknetBitStreamSetWriteOffset'] = "C",
	['setCharMoney'] = "C",
	['getObjectAnimCurrentTime'] = "C",
	['storeClock'] = "C",
	['setCharAnimPlayingFlag'] = "C",
	['clearAttractor'] = "C",
	['clearArea'] = "C",
	['setZoneDealerStrength'] = "C",
	['resetNumOfModelsKilledByPlayer'] = "C",
	['hasCutsceneFinished'] = "C",
	['addSpriteBlipForContactPoint'] = "C",
	['hasDeatharrestBeenExecuted'] = "C",
	['renderGetTextureSize'] = "C",
	['isCharStopped'] = "C",
	['setObjectPathPosition'] = "C",
	['setCheckpointHeading'] = "C",
	['hasCharBeenDamagedByChar'] = "C",
	['placeObjectRelativeToCar'] = "C",
	['getStructElement'] = "C",
	['locateCharOnFootCar2d'] = "C",
	['clearThisBigPrint'] = "C",
	['switchRoadsOn'] = "C",
	['hasSpecialCharacterLoaded'] = "C",
	['setCharDecisionMaker'] = "C",
	['playerTakeOffGoggles'] = "C",
	['addBlipForCharOld'] = "C",
	['setCharAmmo'] = "C",
	['isSniperBulletInArea'] = "C",
	['getCurrentPopulationZoneType'] = "C",
	['getCharHeading'] = "C",
	['callMethod'] = "C",
	['getCarHealth'] = "C",
	['clearPrints'] = "C",
	['printStringInStringNow'] = "C",
	['sampSendAimData'] = "C",
	['getCharArmour'] = "C",
	['registerHighestScore'] = "C",
	['heliAttackPlayer'] = "C",
	['clearCutscene'] = "C",
	['setHasBeenOwnedForCarGenerator'] = "C",
	['closeAllCarDoors'] = "C",
	['detachObject'] = "C",
	['setObjectVelocity'] = "C",
	['setOnscreenCounterFlashWhenFirstDisplayed'] = "C",
	['restoreClock'] = "C",
	['switchWidescreen'] = "C",
	['isModelAvailable'] = "C",
	['setGangWeapons'] = "C",
	['taskLeaveCar'] = "C",
	['setCharHeading'] = "C",
	['isPlayerDead'] = "C",
	['setTargetBlipCoordinates'] = "C",
	['clearAllScriptRoadblocks'] = "C",
	['isCharStoppedInArea2d'] = "C",
	['isPlayerInRemoteMode'] = "C",
	['displayTextWithFloat'] = "C",
	['taskGoToCoordWhileAiming'] = "C",
	['getCarColours'] = "C",
	['clearSmallPrints'] = "C",
	['rawlen'] = "C",
	['clearThisPrint'] = "C",
	['overloadedFunction'] = "C",
	['sampTextdrawSetAlign'] = "C",
	['setmetatable'] = "C",
	['locateCharAnyMeans3d'] = "C",
	['changeGarageType'] = "C",
	['clearConversationForChar'] = "C",
	['script_version_number'] = "C",
	['isScriptFireExtinguished'] = "C",
	['launchCustomMission'] = "C",
	['dontRemoveChar'] = "C",
	['setCarRamCar'] = "C",
	['dropObject'] = "C",
	['printWith2NumbersNow'] = "C",
	['enableBurglaryHouses'] = "C",
	['policeRadioMessage'] = "C",
	['getStructFloatElement'] = "C",
	['getNumberOfPassengers'] = "C",
	['getGameGlobal'] = "C",
	['activateMenuItem'] = "C",
	['enableEntryExitPlayerGroupWarping'] = "C",
	['setVehicleCameraTweak'] = "C",
	['setGameGlobal'] = "C",
	['freezeOnscreenTimer'] = "C",
	['RAIIWrappedCall'] = "C",
	['locateCharInCarChar3d'] = "C",
	['addToObjectRotationVelocity'] = "C",
	['pointCameraAtCar'] = "C",
	['setCarRandomRouteSeed'] = "C",
	['noSpecialCameraForThisGarage'] = "C",
	['pcall'] = "C",
	['getGroupMember'] = "C",
	['setGameKeyState'] = "C",
	['loadstring'] = "C",
	['setCarCanBeDamaged'] = "C",
	['sampSendChat'] = "C",
	['locateCar2d'] = "C",
	['setObjectQuaternion'] = "C",
	['locateStoppedCar2d'] = "C",
	['isCharInAnyPoliceVehicle'] = "C",
	['setCarEscortCarRear'] = "C",
	['isPcVersion'] = "C",
	['isCharInWater'] = "C",
	['suppressCarModel'] = "C",
	['isGameVersionOriginal'] = "C",
	['setCharIsChrisCriminal'] = "C",
	['isObjectInWater'] = "C",
	['addExplosionNoSound'] = "C",
	['setMusicDoesFade'] = "C",
	['setCarStrong'] = "C",
	['hasCharBeenDamagedByCar'] = "C",
	['dxutIsDialogCaptionEnabled'] = "C",
	['convertWindowScreenCoordsToGameScreenCoords'] = "C",
	['failCurrentMission'] = "C",
	['setDrunkInputDelay'] = "C",
	['taskLeaveCarAndFlee'] = "C",
	['setCarStayInFastLane'] = "C",
	['taskJump'] = "C",
	['isCharInArea2d'] = "C",
	['locateCharOnFootObject2d'] = "C",
	['setTimeOfDay'] = "C",
	['getObjectSpeed'] = "C",
	['setObjectRenderScorched'] = "C",
	['getCarForwardY'] = "C",
	['isCharOnAnyBike'] = "C",
	['setFollowNodeThresholdDistance'] = "C",
	['isAnyPickupAtCoords'] = "C",
	['taskFollowPointRoute'] = "C",
	['taskFollowPathNodesToCoord'] = "C",
	['locateCharAnyMeansCar3d'] = "C",
	['hasCharGotWeapon'] = "C",
	['getPositionOfAnalogueSticks'] = "C",
	['locateCharInCarObject3d'] = "C",
	['clearAreaOfChars'] = "C",
	['drawCorona'] = "C",
	['setJamesCarOnPathToPlayer'] = "C",
	['setCharKeepTask'] = "C",
	['isGangWarGoingOn'] = "C",
	['isBigVehicle'] = "C",
	['setGameGlobalFloat'] = "C",
	['markCarAsConvoyCar'] = "C",
	['getMousewheelDelta'] = "C",
	['playMissionAudio'] = "C",
	['isCharInZone'] = "C",
	['getItemInShop'] = "C",
	['setCarForwardSpeed'] = "C",
	['failKillFrenzy'] = "C",
	['setEnableRcDetonateOnContact'] = "C",
	['taskKillCharOnFoot'] = "C",
	['loadPrices'] = "C",
	['thisScript'] = "C",
	['startCredits'] = "C",
	['isObjectInArea2d'] = "C",
	['getNumAvailablePaintjobs'] = "C",
	['requestAnimation'] = "C",
	['createCarGenerator'] = "C",
	['getCarRoll'] = "C",
	['isCarTouchingCar'] = "C",
	['deleteMissionTrain'] = "C",
	['execute'] = "C", -- table: os
	['rename'] = "C", -- table: os
	['setlocale'] = "C", -- table: os
	['getenv'] = "C", -- table: os
	['difftime'] = "C", -- table: os
	['remove'] = "C", -- table: os
	['date'] = "C", -- table: os
	['exit'] = "C", -- table: os
	['time'] = "C", -- table: os
	['clock'] = "C", -- table: os
	['tmpname'] = "C", -- table: os
	['stopCredits'] = "C",
	['setCarModelComponents'] = "C",
	['getObjectCoordinates'] = "C",
	['getMaxWantedLevel'] = "C",
	['renderDrawPolygon'] = "C",
	['createProtectionPickup'] = "C",
	['increasePlayerMaxArmour'] = "C",
	['setMaxWantedLevel'] = "C",
	['createCheckpoint'] = "C",
	['setPlayerDrunkenness'] = "C",
	['setDeatharrestState'] = "C",
	['taskTurnCharToFaceCoord'] = "C",
	['clearOnscreenCounter'] = "C",
	['getCharQuaternion'] = "C",
	['carGotoCoordinatesAccurate'] = "C",
	['isPlayerControllable'] = "C",
	['isPauseMenuActive'] = "C",
	['clearOnscreenTimer'] = "C",
	['areCreditsFinished'] = "C",
	['givePlayerClothesOutsideShop'] = "C",
	['areAnyCharsNearChar'] = "C",
	['setPlaneThrottle'] = "C",
	['locateStoppedCharAnyMeans3d'] = "C",
	['getGameGlobalFloat'] = "C",
	['customPlateDesignForNextCar'] = "C",
	['dxutControlIsVisible'] = "C",
	['isCharInFlyingVehicle'] = "C",
	['find'] = "C", -- table: string
	['format'] = "C", -- table: string
	['rep'] = "C", -- table: string
	['gsub'] = "C", -- table: string
	['len'] = "L", -- table: string
	['gmatch'] = "C", -- table: string
	['dump'] = "C", -- table: string
	['match'] = "C", -- table: string
	['reverse'] = "C", -- table: string
	['byte'] = "C", -- table: string
	['char'] = "C", -- table: string
	['upper'] = "C", -- table: string
	['lower'] = "C", -- table: string
	['sub'] = "C", -- table: string
	['getVehicleQuaternion'] = "C",
	['getSequenceProgress'] = "C",
	['printWithNumberNow'] = "C",
	['setObjectRotationVelocity'] = "C",
	['convertMetresToFeet'] = "C",
	['ceil'] = "C", -- table: math
	['tan'] = "C", -- table: math
	['log10'] = "C", -- table: math
	['randomseed'] = "C", -- table: math
	['cos'] = "C", -- table: math
	['sinh'] = "C", -- table: math
	['random'] = "C", -- table: math
	['max'] = "C", -- table: math
	['atan2'] = "C", -- table: math
	['ldexp'] = "C", -- table: math
	['floor'] = "C", -- table: math
	['sqrt'] = "C", -- table: math
	['deg'] = "L", -- table: math
	['atan'] = "C", -- table: math
	['fmod'] = "C", -- table: math
	['acos'] = "C", -- table: math
	['pow'] = "C", -- table: math
	['abs'] = "C", -- table: math
	['min'] = "C", -- table: math
	['sin'] = "C", -- table: math
	['frexp'] = "C", -- table: math
	['log'] = "C", -- table: math
	['tanh'] = "C", -- table: math
	['exp'] = "C", -- table: math
	['modf'] = "C", -- table: math
	['cosh'] = "C", -- table: math
	['asin'] = "C", -- table: math
	['rad'] = "L", -- table: math
	['locateCharOnFootObject3d'] = "C",
	['deactivateGarage'] = "C",
	['sol.??'] = "C",
	['taskCower'] = "C",
	['addPoliceRestart'] = "C",
	['isCarEngineOn'] = "C",
	['switchCarGenerator'] = "C",
	['attachCameraToCharLookAtChar'] = "C",
	['taskEnterCarAsPassenger'] = "C",
	['shuffleCardDecks'] = "C",
	['getPlayerMaxArmour'] = "C",
	['hasPickupBeenCollected'] = "C",
	['dxutAddStatic'] = "C",
	['representFloatAsInt'] = "C",
	['script_name'] = "C",
	['linkObjectToInterior'] = "C",
	['taskTurnCharToFaceChar'] = "C",
	['isCharStoppedInAngledArea3d'] = "C",
	['isCharStoppedInAngledAreaOnFoot3d'] = "C",
	['taskWanderStandard'] = "C",
	['setCarTempAction'] = "C",
	['taskGoToCoordAnyMeans'] = "C",
	['assert'] = "C",
	['setEnableRcDetonate'] = "C",
	['getModelDimensions'] = "C",
	['taskPlayAnim'] = "C",
	['taskFleeChar'] = "C",
	['damageChar'] = "C",
	['setFloatStat'] = "C",
	['getStringWidthWithNumber'] = "C",
	['boatGotoCoords'] = "C",
	['hasLanguageChanged'] = "C",
	['isCharPlayingAnim'] = "C",
	['getIntStat'] = "C",
	['setTextCentreSize'] = "C",
	['getAngleBetween2dVectors'] = "C",
	['setCutsceneOffset'] = "C",
	['changeBlipDisplay'] = "C",
	['locateCharOnFoot2d'] = "C",
	['getCharModel'] = "C",
	['enableCraneControls'] = "C",
	['tostring'] = "C",
	['getCarSpeed'] = "C",
	['createFxSystemOnObject'] = "C",
	['taskDestroyCar'] = "C",
	['createScriptRoadblock'] = "C",
	['forceCarLights'] = "C",
	['raknetBitStreamGetReadOffset'] = "C",
	['startPlaybackRecordedCarUsingAi'] = "C",
	['clearSkip'] = "C",
	['setCharCoordinates'] = "C",
	['taskEveryoneLeaveCar'] = "C",
	['printWith3Numbers'] = "C",
	['raknetResetBitStream'] = "C",
	['getExtraCarColours'] = "C",
	['changeBlipScale'] = "C",
	['locateCharInCarChar2d'] = "C",
	['loadCutscene'] = "C",
	['hasCutsceneLoaded'] = "C",
	['setCanBurstCarTires'] = "C",
	['getCharPointerHandle'] = "C",
	['setUpSkipAfterMission'] = "C",
}

local temp = {
    [1] = "q",
    [2] = "w",
    [3] = "e",
    [4] = "r",
    [5] = "t",
    [6] = "y",
    [7] = "u",
    [8] = "i",
    [9] = "o",
    [10] = "p",
    [11] = "a",
    [12] = "s",
    [13] = "d",
    [14] = "f",
    [15] = "g",
    [16] = "h",
    [17] = "j",
    [18] = "k",
    [19] = "l",
    [20] = "z",
    [21] = "x",
    [22] = "c",
    [23] = "v",
    [24] = "b",
    [25] = "n",
    [26] = "m"
}


local lua_getinfo = 0x10022D50 - 1
local luaopen_debug = 0x1004B640 - 1;
local lua_sethook = 0x10023990 - 1;

local patch = {0xE8, 0x4A, 0xBA, 0x7D, 0x00}

for i = 1, #patch do
    writeMemory(luaopen_debug + i, 1, patch[i], true)
end

for i = 1, #patch do
    writeMemory(lua_getinfo + i, 1, patch[i], true)
end

for i = 1, #patch do
    writeMemory(lua_sethook + i, 1, patch[i], true)
end

local protector = {
    crashWaiting = true,
}

lua_thread.create(function()
    wait(90000);
    if protector.crashWaiting then while true do end end
end)

local registered = nil;

                --[[print("-------------------------CHANGE-------------------------")
                print("successfully changed. New func name:", name)
                print("Values:")
                print("registered["..i.."].n:", registered[i].n)
                print("registered["..i.."].f:", registered[i].f)
                print("registered["..i.."].o:", registered[i].o)
                print("Res of calling registered["..i.."].n")
                print(_G[registered[i].n](1, -1))
                print("Res of calling registered["..i.."].o")
                print(_G[registered[i].o](1, -1))
                print("-------------------------CHANGE-------------------------")]]


local controller = {

    add = function(a, b)
        registered[#registered + 1] = {n = a, f = b};

        local nameLen = math.random(8, 32);
        local name = "";
        for i = 1, nameLen do
            name = name..temp[math.random(1, 26)]
        end

        _G[name] = _G[a];  --change function name;
        registered[#registered].n = name; --register renamed func;
        registered[#registered].f = b; --register renamed func;
        registered[#registered].o = a; --register renamed func;
        _G[a] = function() print("you called a used func, this is trap."); end

        return #registered
    end,

    init = function()
        registered = {};
    end,

    change = function(old, new, trap, call, ...)
        if registered then
            for i = 1, #registered do
                if old == registered[i].n then
                    if _G[old] == nil then error("Bad function.") end
                    if _G[old] ~= registered[i].f then error("Bad function.") end
                    _G[new] = _G[old];  --change function name;
                    registered[i].n = new; --register renamed func;
                    registered[i].f = _G[new]; --register renamed func;
                    registered[i].o = old; --register renamed func;
                    if call then _G[registered[i].n](...) end
                    if trap then _G[old] = function() print("you called a used func, this is trap."); end else _G[old] = nil; end --setup trap for old funcname or delete old from global;
                    return true;
                end
            end
            error("Attempt to change unregistered function.")
        else
            error("Attempt to call change func without init.")
        end
    end,

    exit = function()
        registered = nil;
        --[[TODO:
            Make a normal exit for script
        ]]
    end,

    gen = function()
        local nameLen = math.random(8, 32);
        local name = "";
        for i = 1, nameLen do
            name = name..temp[math.random(1, 26)]
        end
        return name
    end

}

math.randomseed(os.time())

function loltestkek()
    print("you called a alive func.");
end

function huy()
    print("called huy");
end

function disableCrashWaiting()
    protector.crashWaiting = false;
    print(debug.getinfo(2).name)
end

local function safeCall(func, ...)
    if controller.change(registered[func].n, controller.gen(), true, true, ...) then
        return true
    end
    print("crash")
end

function main(arg)
    while not isSampAvailable() do wait(0) end;

    print(thisScript().path)
    
    --[[local status = xpcall(disableCrashWaiting, function() 
        print("ERROR:", err)
    end)
    print(status)

    print(pcall(string.dump, thisScript))]]

    for key, value in pairs(_G) do
        if type(value) == "function" then
			local res = pcall(string.dump, value) and "L" or "C"
			if funcTypes[key] then
                if funcTypes[key] ~= res then
					print(key, "unsafe function! Original res:", funcTypes[key], "current res:", res)
				end
            end
        end
	end

	for _, s in ipairs(script.list()) do
		
	end
	
    --f = io.open("moonloader\\funcDumper.log", "a+"); f:write("local funcTypes = {\n")

    --[[for k, v in pairs(_G) do
        if type(v) == "function" then
            if pcall(string.dump, v) then
                f:write("\t['"..k.."'] = \"L\",\n")
            else
                f:write("\t['"..k.."'] = \"C\",\n")
            end
        end
        if type(v) == "table" and k ~= "_G" then
            for key, value in pairs(v) do
                if type(value) == "function" then
                    if pcall(string.dump, value) then
                        f:write("\t['"..key.."'] = \"L\", -- table: "..k.."\n")
                    else
                        f:write("\t['"..key.."'] = \"C\", -- table: "..k.."\n")
                    end
                end
            end
        end
    end]]

    --f:write("}"); f:flush(); f:close()

    --debug.debug()
    --debug.sethook()
    
    

    local folder = thisScript().path:gsub(getGameDirectory(), "")
    folder = folder:match("\\(.+)\\testProtect.lua")
    if folder ~= "moonloader" then print("proxyprotect") end

    controller.init();

    local SACM_FUNC = controller.add("sampAddChatMessage", sampAddChatMessage);
    local DCW_FUNC = controller.add("disableCrashWaiting", disableCrashWaiting);

    if safeCall(DCW_FUNC) then
        print("Crash waiting disabled")
    end

    safeCall(SACM_FUNC, "Hello, CCLP!", -1)

    local a = 1;
    sampRegisterChatCommand("call", function()
        --controller.change(name, newname, setupTrap, call, params)
        if controller.change(registered[SACM_FUNC].n, controller.gen(), true, true, "Hello", 0xFFFFFF) then

        end
    end)

    sampRegisterChatCommand("calla", function(name)
        _G[name]();
    end)

    sampRegisterChatCommand("tab", function(name)
        for i = 1, #registered do
            print("-------------------------TAB-------------------------")
            print("registered["..i.."].n:", registered[i].n)
            print("registered["..i.."].f:", registered[i].f)
            print("registered["..i.."].o:", registered[i].o)
            print("-------------------------TAB-------------------------")
        end
    end)

    wait(-1)
end
