--[[
	Project: SA Memory (Available from https://blast.hk/)
	Developers: LUCHA<PERSON>, FYP

	Special thanks:
		plugin-sdk (https://github.com/DK22Pac/plugin-sdk) for the structures and addresses.

	Copyright (c) 2018 BlastHack.
]]

local shared = require 'SAMemory.shared'

shared.ffi.cdef[[
	typedef enum eWeaponType
	{
		WEAPON_UNARMED = 0x0,
		WEAPON_BRASSKNUCKLE = 0x1,
		WEAPON_GOLFCLUB = 0x2,
		WEAPON_NIGHTSTICK = 0x3,
		WEAPON_KNIFE = 0x4,
		WEAPON_BASEBALLBAT = 0x5,
		WEAPON_SHOVEL = 0x6,
		WEAPON_POOLCUE = 0x7,
		WEAPON_KATANA = 0x8,
		WEAPON_CHAINSAW = 0x9,
		WEAPON_DILDO1 = 0xA,
		WEAPON_DILDO2 = 0xB,
		WEAPON_VIBE1 = 0xC,
		WEAPON_VIBE2 = 0xD,
		WEAPON_FLOWERS = 0xE,
		WEAPON_CANE = 0xF,
		WEAPON_GRENADE = 0x10,
		WEAPON_TEARGAS = 0x11,
		WEAPON_MOLOTOV = 0x12,
		WEAPON_ROCKET = 0x13,
		WEAPON_ROCKET_HS = 0x14,
		WEAPON_FREEFALL_BOMB = 0x15,
		WEAPON_PISTOL = 0x16,
		WEAPON_PISTOL_SILENCED = 0x17,
		WEAPON_DESERT_EAGLE = 0x18,
		WEAPON_SHOTGUN = 0x19,
		WEAPON_SAWNOFF = 0x1A,
		WEAPON_SPAS12 = 0x1B,
		WEAPON_MICRO_UZI = 0x1C,
		WEAPON_MP5 = 0x1D,
		WEAPON_AK47 = 0x1E,
		WEAPON_M4 = 0x1F,
		WEAPON_TEC9 = 0x20,
		WEAPON_COUNTRYRIFLE = 0x21,
		WEAPON_SNIPERRIFLE = 0x22,
		WEAPON_RLAUNCHER = 0x23,
		WEAPON_RLAUNCHER_HS = 0x24,
		WEAPON_FTHROWER = 0x25,
		WEAPON_MINIGUN = 0x26,
		WEAPON_SATCHEL_CHARGE = 0x27,
		WEAPON_DETONATOR = 0x28,
		WEAPON_SPRAYCAN = 0x29,
		WEAPON_EXTINGUISHER = 0x2A,
		WEAPON_CAMERA = 0x2B,
		WEAPON_NIGHTVISION = 0x2C,
		WEAPON_INFRARED = 0x2D,
		WEAPON_PARACHUTE = 0x2E,
		WEAPON_UNUSED = 0x2F,
		WEAPON_ARMOUR = 0x30
	} eWeaponType;
]]
