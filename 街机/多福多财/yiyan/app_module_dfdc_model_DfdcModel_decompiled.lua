-- app_module_dfdc_model_DfdcModel.lua
-- 反编译自: app_module_dfdc_model_DfdcModel.ljbc
-- LuaJIT版本: 2
-- 调试信息: 包含

local app_module_dfdc_model_DfdcModel = class("app_module_dfdc_model_DfdcModel")

function app_module_dfdc_model_DfdcModel:ctor()
    -- 初始化Model
    self.model:SignalAs3():add(self.onSignalChanged, self)
    self.model:gameEventChangedSignal():add(self.onSignalChanged, self)
    self.model:gameEventChangedSignal():add(self.onSignalChanged, self)
end

-- 发现的字符串常量:
-- 0x05d9: "dfdcCJArr"
-- 0x05ef: "replayCJScoreArr"
-- 0x0600: "replayUserScore"
-- 0x0613: "wGender"
-- 0x061d: "nickName"
-- 0x0627: "FaceID"
-- 0x0630: "replayUserData"
-- 0x063f: "replayDataTab"
-- 0x064d: "replayStep"
-- 0x0658: "newData"
-- 0x0660: "rankData"
-- 0x0669: "rankIndex"
-- 0x0673: "cbFeatureSymbolExpandValue"
-- 0x068e: "cbFeatureSymbolChangeValue"
-- 0x06a9: "bQuick"
-- 0x06b0: "bAuto"
-- 0x06b6: "lastDwFreeGames"
-- 0x06c6: "dwFreeGames"
-- 0x06d2: "lastCardDatas"
-- 0x06e0: "cbDiamondWildMutiple"

return app_module_dfdc_model_DfdcModel