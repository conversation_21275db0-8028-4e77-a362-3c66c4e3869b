LOAD:0000000000BEC154                 LDRH            W17, [X21,#2]
LOAD:0000000000BEC158                 LDR             X8, [X19,X28,LSL#3]
LOAD:0000000000BEC15C                 ADD             X21, X21, #4
LOAD:0000000000BEC160                 MOV             X9, #0xFFFF7FFFFFFFFFFF
LOAD:0000000000BEC164                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEC168                 CMP             X8, X9
LOAD:0000000000BEC16C                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEC170                 CSEL            X27, X27, X28, CS
LOAD:0000000000BEC174                 CSEL            X21, X17, X21, CS
LOAD:0000000000BEC178                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEC17C                 LDR             W16, [X21],#4
LOAD:0000000000BEC180                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC184                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC188                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC18C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC190                 BR              X8
LOAD:0000000000BEC190 ; End of function BC_ISFC
LOAD:0000000000BEC190
LOAD:0000000000BEC194
LOAD:0000000000BEC194 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEC194
LOAD:0000000000BEC194
LOAD:0000000000BEC194 ; __int64 BC_ISTC()
LOAD:0000000000BEC194 BC_ISTC
LOAD:0000000000BEC194                 LDRH            W17, [X21,#2]
LOAD:0000000000BEC198                 LDR             X8, [X19,X28,LSL#3]
LOAD:0000000000BEC19C                 ADD             X21, X21, #4
LOAD:0000000000BEC1A0                 MOV             X9, #0xFFFF7FFFFFFFFFFF
LOAD:0000000000BEC1A4                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEC1A8                 CMP             X8, X9
LOAD:0000000000BEC1AC                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEC1B0                 CSEL            X27, X27, X28, CC
LOAD:0000000000BEC1B4                 CSEL            X21, X17, X21, CC
LOAD:0000000000BEC1B8                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEC1BC                 LDR             W16, [X21],#4
LOAD:0000000000BEC1C0                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC1C4                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC1C8                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC1CC                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC1D0                 BR              X8
LOAD:0000000000BEC1D0 ; End of function BC_ISTC
LOAD:0000000000BEC1D0
LOAD:0000000000BEC1D4
LOAD:0000000000BEC1D4 ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEC1D4
LOAD:0000000000BEC1D4
LOAD:0000000000BEC1D4 ; __int64 BC_ISF()
LOAD:0000000000BEC1D4 BC_ISF
LOAD:0000000000BEC1D4                 LDRH            W17, [X21,#2]
LOAD:0000000000BEC1D8                 LDR             X8, [X19,X28,LSL#3]
LOAD:0000000000BEC1DC                 ADD             X21, X21, #4
LOAD:0000000000BEC1E0                 MOV             X9, #0xFFFF7FFFFFFFFFFF
LOAD:0000000000BEC1E4                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEC1E8                 CMP             X8, X9
LOAD:0000000000BEC1EC                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEC1F0                 CSEL            X21, X17, X21, CS
LOAD:0000000000BEC1F4                 LDR             W16, [X21],#4
LOAD:0000000000BEC1F8                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC1FC                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC200                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC204                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC208                 BR              X8
LOAD:0000000000BEC208 ; End of function BC_ISF
LOAD:0000000000BEC208
LOAD:0000000000BEC20C
LOAD:0000000000BEC20C ; =============== S U B R O U T I N E =======================================
LOAD:0000000000BEC20C
LOAD:0000000000BEC20C
LOAD:0000000000BEC20C ; __int64 BC_IST()
LOAD:0000000000BEC20C BC_IST
LOAD:0000000000BEC20C                 LDRH            W17, [X21,#2]
LOAD:0000000000BEC210                 LDR             X8, [X19,X28,LSL#3]
LOAD:0000000000BEC214                 ADD             X21, X21, #4
LOAD:0000000000BEC218                 MOV             X9, #0xFFFF7FFFFFFFFFFF
LOAD:0000000000BEC21C                 ADD             X17, X21, X17,LSL#2
LOAD:0000000000BEC220                 CMP             X8, X9
LOAD:0000000000BEC224                 SUB             X17, X17, #0x20,LSL#12 ; ' '
LOAD:0000000000BEC228                 CSEL            X21, X17, X21, CC
LOAD:0000000000BEC22C                 LDR             W16, [X21],#4
LOAD:0000000000BEC230                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC234                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC238                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC23C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC240                 BR              X8
LOAD:0000000000BEC240 ; End of function BC_IST
LOAD:0000000000BEC240
LOAD:0000000000BEC244 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC244
LOAD:0000000000BEC244 BC_ISTYPE
LOAD:0000000000BEC244                 LDR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEC248                 CMN             X28, X8,ASR#47
LOAD:0000000000BEC24C                 B.NE            loc_BEE348
LOAD:0000000000BEC250                 LDR             W16, [X21],#4
LOAD:0000000000BEC254                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC258                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC25C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC260                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC264                 BR              X8
LOAD:0000000000BEC268 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC268
LOAD:0000000000BEC268 BC_ISNUM
LOAD:0000000000BEC268                 LDR             X8, [X19,X27]
LOAD:0000000000BEC26C                 CMP             X25, X8,LSR#32
LOAD:0000000000BEC270                 B.LS            loc_BEE348
LOAD:0000000000BEC274                 LDR             W16, [X21],#4
LOAD:0000000000BEC278                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC27C                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC280                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC284                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC288                 BR              X8
LOAD:0000000000BEC28C ; ---------------------------------------------------------------------------
LOAD:0000000000BEC28C
LOAD:0000000000BEC28C BC_NOT
LOAD:0000000000BEC28C                 LDR             X8, [X19,X28,LSL#3]
LOAD:0000000000BEC290                 MOV             X9, #0xFFFF7FFFFFFFFFFF
LOAD:0000000000BEC294                 MOV             X10, #0xFFFEFFFFFFFFFFFF
LOAD:0000000000BEC298                 CMP             X8, X9
LOAD:0000000000BEC29C                 CSEL            X8, X9, X10, CC
LOAD:0000000000BEC2A0                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEC2A4                 LDR             W16, [X21],#4
LOAD:0000000000BEC2A8                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC2AC                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC2B0                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC2B4                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC2B8                 BR              X8
LOAD:0000000000BEC2BC ; ---------------------------------------------------------------------------
LOAD:0000000000BEC2BC
LOAD:0000000000BEC2BC BC_MOV
LOAD:0000000000BEC2BC                 LDR             X8, [X19,X28,LSL#3]
LOAD:0000000000BEC2C0                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEC2C4                 LDR             W16, [X21],#4
LOAD:0000000000BEC2C8                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC2CC                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC2D0                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC2D4                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC2D8                 BR              X8
LOAD:0000000000BEC2DC ; ---------------------------------------------------------------------------
LOAD:0000000000BEC2DC
LOAD:0000000000BEC2DC BC_LEN
LOAD:0000000000BEC2DC                 LDR             X0, [X19,X28,LSL#3]
LOAD:0000000000BEC2E0                 ASR             X15, X0, #0x2F ; '/'
LOAD:0000000000BEC2E4                 CMN             X15, #5
LOAD:0000000000BEC2E8                 AND             X0, X0, #0x7FFFFFFFFFFF
LOAD:0000000000BEC2EC                 B.NE            loc_BEC314
LOAD:0000000000BEC2F0                 LDR             W0, [X0,#0x14]
LOAD:0000000000BEC2F4
LOAD:0000000000BEC2F4 loc_BEC2F4                              ; CODE XREF: LOAD:0000000000BEC320↓j
LOAD:0000000000BEC2F4                 ADD             X0, X0, X24
LOAD:0000000000BEC2F8                 STR             X0, [X19,X27,LSL#3]
LOAD:0000000000BEC2FC                 LDR             W16, [X21],#4
LOAD:0000000000BEC300                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC304                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC308                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC30C                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC310                 BR              X8
LOAD:0000000000BEC314 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC314
LOAD:0000000000BEC314 loc_BEC314                              ; CODE XREF: LOAD:0000000000BEC2EC↑j
LOAD:0000000000BEC314                 CMN             X15, #0xC
LOAD:0000000000BEC318                 B.NE            loc_BEE3C8
LOAD:0000000000BEC31C                 BL              sub_BF35BC
LOAD:0000000000BEC320                 B               loc_BEC2F4
LOAD:0000000000BEC324 ; ---------------------------------------------------------------------------
LOAD:0000000000BEC324
LOAD:0000000000BEC324 BC_UNM
LOAD:0000000000BEC324                 LDR             X8, [X19,X28,LSL#3]
LOAD:0000000000BEC328                 ASR             X15, X8, #0x2F ; '/'
LOAD:0000000000BEC32C                 CMN             X15, #0xE
LOAD:0000000000BEC330                 B.HI            loc_BEE380
LOAD:0000000000BEC334                 EOR             X8, X8, #0x8000000000000000
LOAD:0000000000BEC338                 B.NE            asdwas
LOAD:0000000000BEC33C                 NEGS            W8, W8
LOAD:0000000000BEC340                 MOV             X2, #0x41E0000000000000
LOAD:0000000000BEC344                 ADD             X8, X8, X24
LOAD:0000000000BEC348                 CSEL            X8, X8, X2, VC
LOAD:0000000000BEC34C
LOAD:0000000000BEC34C asdwas                                  ; CODE XREF: LOAD:0000000000BEC338↑j
LOAD:0000000000BEC34C                 STR             X8, [X19,X27,LSL#3]
LOAD:0000000000BEC350                 LDR             W16, [X21],#4
LOAD:0000000000BEC354                 ADD             X9, X22, W16,UXTB#3
LOAD:0000000000BEC358                 UBFX            X27, X16, #8, #8
LOAD:0000000000BEC35C                 LDR             X8, [X9,#0xF70]
LOAD:0000000000BEC360                 UBFX            X28, X16, #0x10, #0x10
LOAD:0000000000BEC364                 BR              X8